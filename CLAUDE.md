# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a web-based time tracking application that helps users accurately record their time utilization across 84 hours per week (9am-11pm daily), providing visual insights through color-coded categories and Eisenhower matrix prioritization. The application allows for planning and tracking of activities.

## Tech Stack

- **Frontend**: React 18 + TypeScript + Next.js 14
- **Styling**: Tailwind CSS + Headless UI
- **Charts**: Chart.js
- **State Management**: React Context + useReducer
- **Storage**: localStorage
- **Testing**: Jest + React Testing Library
- **AWS Deployment**: AWS CDK with S3 + CloudFront

## Architecture Overview

The application follows a clean architecture pattern with:
- **Presentation Layer**: React components in `src/components/`
- **Business Logic Layer**: Context stores and repositories in `src/stores/` and `src/lib/repositories/`
- **Data Layer**: Models and storage services in `src/lib/models/` and `src/lib/services/`
- **Infrastructure Layer**: AWS CDK stacks in `infrastructure/` and `infrastructure-simple/`

Key architectural patterns:
- Repository pattern for data access abstraction
- Context-based state management with useReducer
- Domain models with business logic encapsulation
- Clean separation between UI, business logic, and data access

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
├── components/             # React components
│   ├── ui/                # Reusable UI components
│   ├── charts/            # Chart components (Eisenhower matrix, weekly progress)
│   ├── forms/             # Form components
│   └── layout/            # Layout components (Header, Sidebar)
├── hooks/                 # Custom React hooks
├── lib/                   # Utilities and business logic
│   ├── models/            # Domain models (Category, TimeEntry, PlannedEntry)
│   ├── repositories/      # Data access layer (TimeTrackingRepository)
│   ├── services/          # Storage services (LocalStorageService)
│   └── utils/             # Utility functions
├── stores/                # React Context stores (TimeTrackingContext)
├── types/                 # TypeScript type definitions
└── __tests__/             # Test files

infrastructure/           # AWS CDK deployment (enhanced version)
infrastructure-simple/    # AWS CDK deployment (simple version)
scripts/                  # Deployment and utility scripts
```

## Common Commands

### Development
- `npm run dev` - Start development server
- `npm run build` - Build production version
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - TypeScript type checking

### Testing
- `npm run test` - Run all tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report

### AWS Deployment
- `npm run deploy:aws` - Standard AWS deployment
- `npm run deploy:aws:enhanced` - Enhanced AWS deployment (recommended)
- `npm run deploy:aws:status` - Check deployment status
- `npm run deploy:aws:prereqs` - Install AWS deployment prerequisites

### Local Deployment
- `npm run deploy:local` - Local deployment
- `npm run deploy:docker` - Docker deployment

### Maintenance
- `npm run clean:build` - Clean build directories
- `npm run fix:next` - Fix Next.js directory issues
- `npm run force:clean:next` - Force clean Next.js cache

## Key Components

- **TimeTrackingContext**: Main state management with categories, time entries, planned entries
- **TimeTrackingRepository**: Data access layer with localStorage persistence
- **TimeGrid**: Main time tracking grid component
- **EisenhowerMatrixChart**: Visual priority matrix
- **WeeklyProgressChart**: Weekly time utilization visualization
