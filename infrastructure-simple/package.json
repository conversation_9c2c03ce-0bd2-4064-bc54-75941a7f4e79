{"name": "infrastructure-simple", "version": "0.1.0", "bin": {"infrastructure-simple": "bin/infrastructure-simple.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.7.9", "aws-cdk": "2.1026.0", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"aws-cdk-lib": "^2.211.0", "constructs": "^10.4.2"}}