#git related
.git/
.github/
.vscode/
.idea/
.augment/
.next/
.cache/
.swc/
dist*/
build/
node_modules/
coverage/
*.log
*.log*
.gitignore

*.mdx
*.mdown
*.markdown
*.txt
*.csv
!README.md

AWS-DEPLOY.md
CLAUDE.md
GEMINI.md

# Dependencies
node_modules/
npm-debug.log
yarn-error.log
yarn-debug.log
package-lock.json
yarn.lock

# Production
/dist
/build

.next/
.cache/
Dockerfile
.dockerignore

# Folders to keep locally but not in remote
/infrastructure/
/infrastructure-simple/
/out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# OS
.DS_Store
Thumbs.db

# photos, txt
*.png
*.jpg
*.jpeg
*.txt
*.gif
*.s*p
*.csv
*.pdf
*.doc
*.docx
*.ppt
*.pptx
*.xls
*.xlsx
*.odt
*.ods
*.odp
*.odg
