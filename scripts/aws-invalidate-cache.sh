#!/bin/bash

# Time Tracking App - CloudFront 缓存失效脚本
# 此脚本用于刷新 CloudFront CDN 缓存

set -e  # 遇到错误时退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # 无颜色

# 输出函数
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 配置
STACK_NAME="time-tracking-app"
# 从AWS配置文件中读取区域，如果未设置则使用us-east-1作为默认值
DEFAULT_REGION=$(aws configure get region 2>/dev/null || echo "us-east-1")
REGION="${AWS_REGION:-$DEFAULT_REGION}"

print_status "CloudFront 缓存失效"
print_status "Stack 名称: $STACK_NAME"
print_status "区域: $REGION"

# 检查 AWS CLI 是否已安装
if ! command -v aws &> /dev/null; then
    print_error "未安装 AWS CLI。请安装 AWS CLI 后重试。"
    exit 1
fi

# 检查 AWS 凭证
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "未配置 AWS 凭证。请先运行 'aws configure'。"
    exit 1
fi

# 获取 CloudFront 分发 ID
print_status "获取 CloudFront 分发 ID..."
DISTRIBUTION_ID=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --region $REGION \
    --query 'Stacks[0].Outputs[?OutputKey==`CloudFrontDistributionId`].OutputValue' \
    --output text)

if [ -z "$DISTRIBUTION_ID" ] || [ "$DISTRIBUTION_ID" == "None" ]; then
    print_error "无法获取 CloudFront 分发 ID。请确保已部署应用且包含 CloudFront。"
    exit 1
fi

print_status "CloudFront 分发 ID: $DISTRIBUTION_ID"

# 创建缓存失效
print_status "创建缓存失效请求..."
INVALIDATION_ID=$(aws cloudfront create-invalidation \
    --distribution-id $DISTRIBUTION_ID \
    --paths "/*" \
    --query 'Invalidation.Id' \
    --output text)

if [ -z "$INVALIDATION_ID" ]; then
    print_error "创建缓存失效请求失败。"
    exit 1
fi

print_success "已创建缓存失效请求 (ID: $INVALIDATION_ID)"
print_status "缓存失效可能需要 5-15 分钟完成"

# 获取应用 URL
CLOUDFRONT_URL=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --region $REGION \
    --query 'Stacks[0].Outputs[?OutputKey==`CloudFrontURL`].OutputValue' \
    --output text)

if [ -n "$CLOUDFRONT_URL" ] && [ "$CLOUDFRONT_URL" != "None" ]; then
    print_success "应用 URL: $CLOUDFRONT_URL"
fi

print_status "可以使用以下命令检查缓存失效状态:"
print_status "aws cloudfront get-invalidation --distribution-id $DISTRIBUTION_ID --id $INVALIDATION_ID"