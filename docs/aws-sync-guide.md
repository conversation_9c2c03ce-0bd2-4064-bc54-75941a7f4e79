# AWS 部署与同步指南

本指南详细说明如何在 AWS 上进行初始部署，以及如何在本地更改后同步到 AWS。

## 初始部署

### 先决条件

1. 安装并配置 AWS CLI
   ```bash
   # 安装 AWS CLI
   curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
   unzip awscliv2.zip
   sudo ./aws/install
   
   # 配置 AWS 凭证
   aws configure
   ```

2. 安装 Node.js 和 npm
   ```bash
   # 使用 nvm 安装 Node.js
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
   nvm install 18.17
   nvm use 18.17
   ```

### 部署步骤

1. 克隆项目（如果尚未克隆）
   ```bash
   git clone <repository-url>
   cd time_track_app2
   ```

2. 使用增强版部署脚本
   ```bash
   ./scripts/aws-deploy.sh enhanced
   ```

3. 验证部署
   ```bash
   # 检查部署状态
   ./scripts/aws-status.sh
   ```

## 本地更改后同步到 AWS

### 方法 1：使用增强版部署脚本（推荐）

这是推荐的方法，它会自动处理构建、部署和缓存失效：

```bash
# 在本地更改后运行
./scripts/aws-deploy.sh enhanced
```

### 方法 2：手动同步

如果您需要更精细的控制，可以手动执行以下步骤：

1. 构建应用
   ```bash
   npm run build
   ```

2. 部署到 AWS
   ```bash
   cd infrastructure
   npx cdk deploy
   ```

3. 清除 CloudFront 缓存（如果使用了 CloudFront）
   ```bash
   ./scripts/aws-invalidate-cache.sh
   ```

## 常见问题解决

### 1. 构建输出目录不存在

如果您遇到 "Cannot find asset at /path/to/out" 错误，请检查 `next.config.js` 文件：

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 确保输出配置正确
  output: 'export',
  distDir: 'out',
}

module.exports = nextConfig
```

### 2. AWS 凭证问题

如果遇到 "Unable to locate credentials" 错误：

```bash
# 检查 AWS 凭证是否有效
aws sts get-caller-identity

# 如果无效，重新配置
aws configure
```

### 3. 部署失败

如果部署失败，可以查看详细的错误信息：

```bash
# 查看 CloudFormation 堆栈事件
aws cloudformation describe-stack-events \
  --stack-name time-tracking-app \
  --query 'StackEvents[?ResourceStatus==`CREATE_FAILED` || ResourceStatus==`UPDATE_FAILED`]'
```

## 部署后维护

### 监控应用

部署后，您可以使用 AWS CloudWatch 监控应用性能：

```bash
# 查看 CloudWatch 指标
aws cloudwatch get-metric-statistics \
  --namespace AWS/S3 \
  --metric-name NumberOfObjects \
  --dimensions Name=BucketName,Value=<your-bucket-name> \
  --start-time $(date -d '1 day ago' +%Y-%m-%dT%H:%M:%S) \
  --end-time $(date +%Y-%m-%dT%H:%M:%S) \
  --period 86400 \
  --statistics Average
```

### 清理资源

如果不再需要应用，可以删除所有资源：

```bash
# 删除 CloudFormation 堆栈
aws cloudformation delete-stack --stack-name time-tracking-app
```