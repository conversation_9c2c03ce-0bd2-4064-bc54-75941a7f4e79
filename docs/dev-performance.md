# 开发性能优化指南

## 问题

Next.js 开发服务器在某些情况下可能会变得缓慢，特别是当项目变大或者有大量模块需要编译时。这可能导致：

- 开发服务器启动缓慢
- 热重载响应迟钝
- 频繁的重新编译
- 浏览器加载时间长

## 优化解决方案

我们添加了几个脚本来解决这些问题：

### 1. 优化的开发服务器

```bash
npm run dev:fast
```

这个命令会：
- 清理 Next.js 缓存
- 增加 Node.js 内存限制
- 禁用遥测
- 使用优化的配置启动开发服务器

### 2. 清理缓存

如果您遇到奇怪的编译错误或性能问题：

```bash
npm run clean
```

这个命令会清理：
- Next.js 缓存 (.next 目录)
- 构建输出 (out 目录)
- Node 模块缓存

### 3. 快速清理并重启

如果您只想快速清理并重启开发服务器：

```bash
npm run dev:clean
```

## next.config.js 优化

我们已经更新了 Next.js 配置以提高开发性能：

- 开发模式下禁用了严格模式
- 优化了 webpack 监视选项
- 使用 SWC 进行更快的编译
- 根据环境自动调整配置

## 浏览器缓存

如果仍然遇到问题，请尝试清理浏览器缓存：

- Chrome: Ctrl+Shift+Del → 缓存的图片和文件 → 清除数据
- Firefox: Ctrl+Shift+Del → 缓存 → 清除
- Edge: Ctrl+Shift+Del → 缓存的图片和文件 → 清除

## 其他提示

- 避免在开发过程中频繁修改大型依赖文件
- 考虑使用模块联邦或代码拆分来减少编译时间
- 对于大型项目，考虑使用 Turborepo 或其他 monorepo 工具