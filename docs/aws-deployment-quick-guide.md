# AWS 快速部署指南

## 概述

本指南提供了使用已配置的 AWS 凭证快速部署 Time Tracking App 的步骤。此部署方法利用您已经设置好的 `~/.aws/config` 和 `~/.aws/credentials` 文件，无需额外配置即可部署。

## 前提条件

1. **已配置的 AWS 凭证**：确保 `~/.aws/config` 和 `~/.aws/credentials` 文件已正确设置
2. **Node.js**：版本 18.17 或更高
3. **npm**：用于安装依赖和运行脚本

## 快速部署步骤

### 1. 验证 AWS 凭证

首先，确认您的 AWS 凭证已正确配置：

```bash
aws sts get-caller-identity
```

如果命令成功执行并显示您的 AWS 账户信息，则表示凭证配置正确。

### 2. 部署应用

执行以下命令开始部署：

```bash
./scripts/aws-deploy.sh enhanced
```

此命令将：
- 自动从 `~/.aws/config` 读取您的默认区域设置
- 安装所有必要的依赖
- 构建应用程序
- 部署到 AWS CloudFormation
- 提供应用程序访问 URL

### 3. 访问应用

部署完成后，终端将显示应用的访问 URL。您可以直接点击该链接或将其复制到浏览器中打开。

## 区域设置

部署脚本会自动从 `~/.aws/config` 文件中读取默认区域。如果您想使用不同的区域，可以通过环境变量指定：

```bash
AWS_REGION=eu-west-1 ./scripts/aws-deploy.sh enhanced
```

## 故障排除

如果遇到部署问题，请尝试以下步骤：

1. **检查 AWS 凭证**：确保凭证有效且具有足够的权限
   ```bash
   aws sts get-caller-identity
   ```

2. **检查区域设置**：确认您的默认区域设置正确
   ```bash
   aws configure get region
   ```

3. **检查先决条件**：运行先决条件检查脚本
   ```bash
   ./scripts/aws-prereqs.sh
   ```

4. **查看部署状态**：
   ```bash
   ./scripts/aws-status.sh
   ```

## 后续步骤

成功部署后，您可能需要：

1. **设置自定义域名**：如果您有自己的域名，可以将其指向 CloudFront 分发
2. **配置 HTTPS**：使用 AWS Certificate Manager 设置 SSL 证书
3. **设置监控**：配置 CloudWatch 警报以监控应用性能

有关更多详细信息，请参阅完整的 [AWS 部署指南](./aws-deployment-guide.md)。