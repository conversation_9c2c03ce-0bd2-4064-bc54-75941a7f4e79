产品需求文档 (PRD) - Version 2.2 (Next Phase Features)
产品愿景

帮助个人在一周的 12 (9am - 11pm) * 7 = 84 小时里进行**计划制定**和**实际追踪**，通过计划vs实际对比 + 颜色标签 + 四象限优先级 + 周度图表，让用户一眼看出计划执行情况和时间分配效果，从而优化效率与生活平衡。

核心价值主张：Plan → Track → Compare → Optimize

## ✅ 已解决问题 (Resolved Issues)

### ✅ 问题1: 用户无法添加新分类 - 已修复
- 解决方案：修复了表单重置和状态管理问题
- 状态：✅ **正常工作** - 用户现在可以添加分类并立即看到
- 技术实现：改进了React状态更新处理，添加了表单重置逻辑

### ✅ 问题2: 统计页面无图表显示 - 已实现
- 解决方案：实现了完整的图表可视化系统
- 状态：✅ **正常工作** - 提供全面的数据可视化套件
- 功能包括：分类分布饼图、周进度条、四象限优先级分布图

## ✅ 已完成功能 (Completed Features)

### ✅ 需求1: 增强交互式图表 - 已实现
- 现状：✅ **完成** - 提供完整的交互式数据探索功能
- 实现功能：
  - ✅ 点击图表片段进行数据筛选
  - ✅ 自定义时间范围选择（DateRangePicker组件）
  - ✅ 分类显示/隐藏切换（CategoryFilterPanel组件）
  - ✅ 图表导出功能（PNG和CSV格式）
  - ✅ 综合测试覆盖

## 🚧 当前开发阶段 (Current Development Phase)

### ✅ 需求2: 计划vs执行分割视图 (P0 - 核心功能) - 已完成
- 现状：✅ **完成** - 每个小时格分为计划(左)和实际(右)两部分，宽度受限且可编辑
- 目标：每个小时格分为计划(左)和实际(右)两部分
- 实现功能：
  - ✅ 双区域时间格：左侧计划 + 右侧执行
  - ✅ 简单计划界面：分类选择器
  - ✅ 执行状态可视化指示器
  - ✅ 数据模型扩展：支持计划和实际条目
  - ✅ 向后兼容：保持现有数据结构

## 🚧 当前开发阶段 (Current Development Phase)

### ✅ 需求3: 图表可视化改进 (P1 - 用户体验提升) 已完成
- 现状：✅ **问题已解决** - 左侧边栏宽度现在完全可调整
- 实现结果：
  - **架构修复**：移除了嵌套ResizablePanel冲突，采用清洁的单一包装器架构
  - **功能恢复**：恢复所有缺失的组件导入和功能
  - **用户体验**：用户可以调整统计面板宽度（350-800px）以获得最佳图表查看效果
  - **上下文行为**：分类标签页（320px）和统计标签页（450px）使用不同的默认宽度
- 技术实现：
  - ✅ 清洁架构：page.tsx中的单一ResizablePanel包装器
  - ✅ 组件完整性：Sidebar.tsx仅包含内容，无ResizablePanel嵌套
  - ✅ 视觉增强：改进的调整手柄可见性和交互性
  - ✅ 响应式图表：图表动态适应面板宽度变化
  - ✅ 持久化存储：分类和统计标签页分别保存宽度设置

### 🎯 需求4: UI界面优化 (P2 - 界面完善) 已完成
- 目标：针对新的可调整面板进行小幅界面改进
- 具体改进项目：
  - **图表筛选器布局**：将日期范围筛选器移至分类可见性筛选器上方（当前为左右布局）
  - **视觉层次**：改进筛选面板中的间距和对齐
  - **响应式优化**：针对新的可调整面板宽度优化布局
- 预期效果：
  - 更好的垂直空间利用
  - 更清晰的视觉层次
  - 更直观的筛选流程（先日期，后分类）
  - 更好的移动端响应性

### 实现优先级
1. **可调整面板宽度** - 创建ResizablePanel组件允许用户调整左侧面板
2. **图表响应性** - 确保图表根据面板宽度变化重新计算尺寸
3. **增强艾森豪威尔矩阵** - 使用比例形状或替代图表类型提供更直观的可视化
4. **保持交互功能** - 维护所有现有的交互式图表功能

用户画像

工程师 / 独立开发者：希望追踪 coding、system design、AI 使用等类别。

职场人士：需要在工作 / 运动 / 家庭 / 学习之间找到平衡。

个人提升用户：希望用 Eisenhower 四象限区分重要 vs 紧急。

核心需求（用户故事）

## 计划功能 (Planning)
我可以为未来任意周的时间段制定计划，例如"下周一10am-11am学习AI LangGraph"。

我可以快速跳转到任意周进行计划制定（日期选择器 + 快速导航）。

我可以复制上周计划到本周，或创建重复计划模板。

## 追踪功能 (Tracking)
我可以在 Web 界面上为每个小时添加**实际执行**的活动类别。

我可以看到计划vs实际的对比，未执行计划显示为不同状态。

我可以给活动标记重要性和紧急性，系统自动分配到四象限并用不同符号/颜色区分。

## 分类管理 (Categories)
我可以实时创建新类别，新类别立即在界面中可用。

我可以为类别配置颜色标签，在日/周视图中直观展示。

## 可视化分析 (Analytics)
我可以在 Dashboard 图表里查看：

计划vs实际执行率对比图表。

本周各类别所占时长（饼图/条形图）- 基于真实数据。

四象限分布（带符号和颜色区分）：
- Q1 重要紧急：🔥 红色
- Q2 重要不紧急：⭐ 绿色
- Q3 不重要紧急：⚡ 黄色
- Q4 不重要不紧急：💤 灰色

每日时间分布热力图和计划完成度。

## 数据管理 (Data)
我可以导出计划和实际数据的对比报告（CSV/JSON）。

我可以使用自动化脚本进行本地和云端部署。

非功能性需求

响应式 Web（桌面 + 移动）。

本地存储（localStorage）保存；未来可扩展云端账号。

轻量化，首次加载 ≤ 2s。

成功指标

每周 ≥ 80% 小时被记录。

用户能在 <5 秒内定位并添加一条记录。

用户对图表的“直观度”打分 ≥ 4/5。
