import { TimeTrackingProvider } from '@/stores/TimeTrackingContext';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Time Tracker - Eisenhower Matrix',
  description: 'Track your time with Eisenhower matrix prioritization for better productivity',
  keywords: ['time tracking', 'productivity', 'eisenhower matrix', 'time management'],
  authors: [{ name: 'Time Tracker App' }],
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className} suppressHydrationWarning>
        <TimeTrackingProvider>
          <div className="min-h-screen bg-gray-50">
            {children}
          </div>
        </TimeTrackingProvider>
      </body>
    </html>
  );
}
