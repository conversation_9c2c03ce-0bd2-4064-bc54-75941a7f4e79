(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2106:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>em});var r=s(5155),a=s(2115),l=s(5194);function n(e){let{size:t="md",className:s}=e;return(0,r.jsx)("div",{className:(0,l.cn)("loading-spinner",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[t],s)})}let i=(0,a.forwardRef)((e,t)=>{let{className:s,variant:a="primary",size:i="md",isLoading:o,children:c,disabled:d,...m}=e;return(0,r.jsxs)("button",{ref:t,className:(0,l.cn)("inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-500",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-800 focus:ring-gray-500",danger:"bg-red-500 hover:bg-red-600 text-white focus:ring-red-500",ghost:"hover:bg-gray-100 text-gray-700 focus:ring-gray-500"}[a],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[i],s),disabled:d||o,...m,children:[o&&(0,r.jsx)(n,{size:"sm",className:"mr-2"}),c]})});i.displayName="Button";var o=s(3086),c=s(9867);function d(){let{state:e}=(0,o.A)(),[t,s]=(0,a.useState)(!1),n=async()=>{s(!0);try{let t=(0,l._2)(e.currentWeek).map(l.bU),s=e.timeEntries.filter(e=>t.includes(e.date)).map(t=>{var s,r;let a=e.categories.find(e=>e.id===t.categoryId),n=new Date(t.date);return{Date:t.date,Day:n.toLocaleDateString("en-US",{weekday:"long"}),Time:(0,l.KY)(t.hour),Category:(null==a?void 0:a.name)||"Unknown",Important:t.isImportant?"Yes":"No",Urgent:t.isUrgent?"Yes":"No",Quadrant:(s=t.isImportant,r=t.isUrgent,s&&r?"Q1 (Do First)":s&&!r?"Q2 (Schedule)":!s&&r?"Q3 (Delegate)":"Q4 (Eliminate)"),Description:t.description||""}});s.sort((e,t)=>{let s=e.Date.localeCompare(t.Date);return 0!==s?s:e.Time.localeCompare(t.Time)});let r=Object.keys(s[0]||{}),a=[r.join(","),...s.map(e=>r.map(t=>{let s=e[t];return'"'.concat(String(s).replace(/"/g,'""'),'"')}).join(","))].join("\n"),n=new Blob([a],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a"),o=URL.createObjectURL(n);i.setAttribute("href",o),i.setAttribute("download","time-tracking-".concat((0,l.bU)(e.currentWeek),".csv")),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(o)}catch(e){console.error("Failed to export CSV:",e),alert("Failed to export data. Please try again.")}finally{s(!1)}};return(0,r.jsxs)(i,{onClick:n,variant:"secondary",size:"sm",isLoading:t,className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})}var m=s(2192),x=s(851),u=s(3796),g=s(7326),h=s(6485),y=s(4033);function p(e){let{value:t,onChange:s,className:n}=e,[i,o]=(0,a.useState)(!1),[c,d]=(0,a.useState)(t),p=(0,a.useRef)(null);(0,a.useEffect)(()=>{function e(e){p.current&&!p.current.contains(e.target)&&o(!1)}return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let b=e=>{let t=(0,m.k)(e,{weekStartsOn:1});d(t),s(t),o(!1)},f=e=>{let t=new Date(c);t.setMonth(c.getMonth()+("next"===e?1:-1)),d(t)},j=e=>{let t=new Date;return e.toDateString()===t.toDateString()},N=e=>{let s=(0,m.k)(e,{weekStartsOn:1}),r=(0,m.k)(t,{weekStartsOn:1});return s.getTime()===r.getTime()};return(0,r.jsxs)("div",{className:(0,l.cn)("relative",n),ref:p,children:[(0,r.jsxs)("button",{onClick:()=>o(!i),className:"flex items-center space-x-2 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 text-gray-500"}),(0,r.jsxs)("span",{className:"text-gray-700",children:["Week of ",(0,g.GP)((0,m.k)(t,{weekStartsOn:1}),"MMM d, yyyy")]}),(0,r.jsx)(y.A,{className:(0,l.cn)("h-4 w-4 text-gray-400 transition-transform",i&&"rotate-180")})]}),i&&(0,r.jsxs)("div",{className:"absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[320px]",children:[(0,r.jsxs)("div",{className:"p-3 border-b border-gray-200",children:[(0,r.jsx)("h4",{className:"text-xs font-medium text-gray-500 mb-2",children:"Quick Select"}),(0,r.jsx)("div",{className:"space-y-1",children:(()=>{let e=new Date,t=(0,m.k)(e,{weekStartsOn:1});return[{label:"This Week",date:t},{label:"Next Week",date:(0,x.J)(t,1)},{label:"Last Week",date:(0,u.k)(t,1)},{label:"Next Month",date:(0,x.J)(t,4)},{label:"Last Month",date:(0,u.k)(t,4)}]})().map(e=>(0,r.jsx)("button",{onClick:()=>b(e.date),className:"w-full text-left px-2 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors",children:e.label},e.label))})]}),(0,r.jsxs)("div",{className:"p-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("button",{onClick:()=>f("prev"),className:"p-1 hover:bg-gray-100 rounded transition-colors",children:(0,r.jsx)(y.A,{className:"h-4 w-4 rotate-90"})}),(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:(0,g.GP)(c,"MMMM yyyy")}),(0,r.jsx)("button",{onClick:()=>f("next"),className:"p-1 hover:bg-gray-100 rounded transition-colors",children:(0,r.jsx)(y.A,{className:"h-4 w-4 -rotate-90"})})]}),(0,r.jsx)("div",{className:"grid grid-cols-7 gap-1 mb-2",children:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"].map(e=>(0,r.jsx)("div",{className:"text-xs font-medium text-gray-500 text-center py-1",children:e},e))}),(0,r.jsx)("div",{className:"grid grid-cols-7 gap-1",children:(()=>{let e=c.getMonth(),t=new Date(c.getFullYear(),e,1),s=(0,m.k)(t,{weekStartsOn:1}),r=[];for(let e=0;e<42;e++){let t=new Date(s);t.setDate(s.getDate()+e),r.push(t)}return r})().map((e,t)=>(0,r.jsx)("button",{onClick:()=>b(e),className:(0,l.cn)("text-sm p-2 rounded transition-colors",e.getMonth()===c.getMonth()?"text-gray-900":"text-gray-400",j(e)&&"bg-primary-100 text-primary-700 font-medium",N(e)&&"bg-primary-500 text-white",!N(e)&&!j(e)&&"hover:bg-gray-100"),children:e.getDate()},t))})]})]})]})}var b=s(6983),f=s(368),j=s(7937),N=s(1360);function v(){let{state:e,actions:t}=(0,o.A)(),[s,n]=(0,a.useState)(!1),c=async()=>{if(confirm("Are you sure you want to clear all time entries for this week? This action cannot be undone.")){n(!0);try{await t.clearWeekData(e.currentWeek)}catch(e){console.error("Failed to clear week data:",e)}finally{n(!1)}}};return(0,r.jsx)("header",{className:"bg-white border-b border-gray-200 px-4 md:px-6 py-3 md:py-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-3",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-8 h-8 md:w-10 md:h-10 bg-primary-500 rounded-lg",children:(0,r.jsx)(b.A,{className:"h-4 w-4 md:h-6 md:w-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg md:text-xl font-bold text-gray-900",children:"Time Tracker"}),(0,r.jsx)("p",{className:"text-xs md:text-sm text-gray-600 hidden sm:block",children:"Eisenhower Matrix"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4",children:[(0,r.jsx)(i,{variant:"ghost",size:"sm",onClick:()=>{let s=(0,l.oE)(e.currentWeek);t.setCurrentWeek(s)},className:"p-1.5 md:p-2 min-w-[44px] min-h-[44px] flex items-center justify-center",title:"Previous Week",children:(0,r.jsx)(f.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-1 md:space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm md:text-lg font-semibold text-gray-900 text-center",children:[(0,r.jsx)("span",{className:"md:hidden",children:(0,l.k2)(e.currentWeek).split(" - ")[0].slice(-5)}),(0,r.jsx)("span",{className:"hidden md:inline",children:(0,l.k2)(e.currentWeek)})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 md:space-x-2",children:[(0,r.jsx)(i,{variant:"ghost",size:"sm",onClick:()=>{t.setCurrentWeek((0,l.lV)())},className:"text-xs text-primary-600 hover:text-primary-700 px-2 py-1 min-h-[32px]",children:"Today"}),(0,r.jsx)(p,{value:e.currentWeek,onChange:t.setCurrentWeek,className:"text-xs"})]})]}),(0,r.jsx)(i,{variant:"ghost",size:"sm",onClick:()=>{let s=(0,l.Y6)(e.currentWeek);t.setCurrentWeek(s)},className:"p-1.5 md:p-2 min-w-[44px] min-h-[44px] flex items-center justify-center",title:"Next Week",children:(0,r.jsx)(j.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 md:space-x-3",children:[(0,r.jsxs)(i,{variant:"ghost",size:"sm",onClick:c,disabled:s,className:"text-red-600 hover:text-red-700 hover:bg-red-50 min-w-[44px] min-h-[44px] px-2 md:px-3",title:"Clear all entries for this week",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 md:mr-2"}),(0,r.jsx)("span",{className:"hidden md:inline",children:s?"Clearing...":"Clear Week"})]}),(0,r.jsx)(d,{})]})]})})}var w=s(8566);function k(e){let{onSuccess:t,onCancel:s,initialData:l}=e,{actions:n}=(0,o.A)(),[c,d]=(0,a.useState)({name:(null==l?void 0:l.name)||"",color:(null==l?void 0:l.color)||w.Gv[0]}),[m,x]=(0,a.useState)(!1),[u,g]=(0,a.useState)(null),h=!!l,y=async e=>{if(e.preventDefault(),!c.name.trim())return void g("Category name is required");x(!0),g(null);try{h?await n.updateCategory(l.id,c):await n.createCategory(c),d({name:"",color:"#3B82F6"}),t()}catch(e){g(e instanceof Error?e.message:"Failed to save category")}finally{x(!1)}};return(0,r.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,r.jsx)("div",{children:(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:h?"Edit Category":"Add New Category"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"categoryName",className:"block text-xs font-medium text-gray-700 mb-1",children:"Name"}),(0,r.jsx)("input",{id:"categoryName",type:"text",value:c.name,onChange:e=>d({...c,name:e.target.value}),placeholder:"e.g., Work/Coding",className:"input-field text-sm",maxLength:50,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-2",children:"Color"}),(0,r.jsx)("div",{className:"grid grid-cols-6 gap-2",children:w.Gv.map(e=>(0,r.jsx)("button",{type:"button",onClick:()=>d({...c,color:e}),className:"w-8 h-8 rounded-lg border-2 transition-all ".concat(c.color===e?"border-gray-900 scale-110":"border-gray-300 hover:border-gray-400"),style:{backgroundColor:e},title:e},e))})]}),u&&(0,r.jsx)("div",{className:"text-xs text-red-600 bg-red-50 p-2 rounded",children:u}),(0,r.jsxs)("div",{className:"flex space-x-2 pt-2",children:[(0,r.jsxs)(i,{type:"submit",size:"sm",isLoading:m,className:"flex-1",children:[h?"Update":"Add"," Category"]}),(0,r.jsx)(i,{type:"button",variant:"secondary",size:"sm",onClick:s,disabled:m,children:"Cancel"})]})]})}var C=s(1190);function S(){let{state:e,actions:t}=(0,o.A)(),[s,l]=(0,a.useState)(null),[n,c]=(0,a.useState)(null),d=async e=>{try{await t.deleteCategory(e),c(null)}catch(e){console.error("Failed to delete category:",e)}},m=()=>{l(null)};return 0===e.categories.length?(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)("p",{className:"text-sm",children:"No categories yet"}),(0,r.jsx)("p",{className:"text-xs mt-1",children:"Add your first category to get started"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[e.categories.map(e=>(0,r.jsx)("div",{children:s===e.id?(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-3 bg-gray-50",children:(0,r.jsx)(k,{initialData:e,onSuccess:m,onCancel:()=>l(null)})}):(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors",children:[(0,r.jsxs)("div",{className:"flex items-center flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"category-color-dot flex-shrink-0",style:{backgroundColor:e.color}}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900 truncate",children:e.name})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 ml-2",children:[(0,r.jsx)("button",{onClick:()=>{l(e.id)},className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",title:"Edit category",children:(0,r.jsx)(C.A,{className:"h-3 w-3"})}),(0,r.jsx)("button",{onClick:()=>c(e.id),className:"p-1 text-gray-400 hover:text-red-600 transition-colors",title:"Delete category",children:(0,r.jsx)(N.A,{className:"h-3 w-3"})})]})]})},e.id)),n&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-sm w-full p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Delete Category"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Are you sure you want to delete this category? This action cannot be undone."}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(i,{variant:"danger",onClick:()=>d(n),className:"flex-1",children:"Delete"}),(0,r.jsx)(i,{variant:"secondary",onClick:()=>c(null),className:"flex-1",children:"Cancel"})]})]})})]})}let I=(0,a.createContext)(void 0);function A(e){let{children:t}=e,[s,l]=(0,a.useState)("categories");return(0,r.jsx)(I.Provider,{value:{activeTab:s,setActiveTab:l},children:t})}function D(){let e=(0,a.useContext)(I);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}var E=s(814),T=s(1524),U=s(1607),F=s(877);function W(e){let{timeEntries:t,categories:s,className:l="",onCategoryClick:n,containerWidth:i=320,selectedCategories:o=[]}=e,d=(0,a.useRef)(null),m=(0,a.useMemo)(()=>{let e={};t.forEach(t=>{e[t.categoryId]=(e[t.categoryId]||0)+1});let r=s.filter(t=>e[t.id]>0);if(o.length>0&&(r=r.filter(e=>o.includes(e.id))),0===r.length)return null;let a=r.map(e=>e.name),l=r.map(t=>e[t.id]),n=r.map(e=>e.color),i=r.map(e=>e.color),c=r.map(e=>e.id);return{labels:a,datasets:[{label:"Hours",data:l,backgroundColor:n.map(e=>e+"80"),borderColor:i,borderWidth:2,hoverBackgroundColor:n,hoverBorderWidth:3,categoryIds:c}]}},[t,s,o]),x=(0,a.useMemo)(()=>({responsive:!0,maintainAspectRatio:!1,animation:{duration:i<300?0:750},onClick:(e,t)=>{if(t.length>0&&n&&m){let e=t[0].index;n(m.datasets[0].categoryIds[e])}},plugins:{legend:{position:"bottom",align:"center",labels:{padding:i<350?12:20,usePointStyle:!0,font:{size:i<350?11:12,family:"system-ui"},boxWidth:i<350?30:40},maxWidth:i-40},tooltip:{callbacks:{label:function(e){let t=e.label||"",s=e.parsed,r=(s/e.dataset.data.reduce((e,t)=>e+t,0)*100).toFixed(1);return"".concat(t,": ").concat(s,"h (").concat(r,"%) - Click to filter")}},padding:i<350?8:12,bodyFont:{size:i<350?11:13}}}}),[i,n,m]);if(!m)return(0,r.jsx)("div",{className:"flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 ".concat(l),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-2",children:"\uD83D\uDCCA"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"No time entries yet"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"Add some time entries to see the distribution"})]})});let u=o.length>0;return(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-4 ".concat(l),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h3",{className:"text-sm font-medium text-gray-900",children:["Category Distribution",u&&(0,r.jsxs)("span",{className:"ml-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded",children:["Filtered (",o.length," categories)"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[m&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("button",{onClick:()=>{if(d.current){let e=d.current.toBase64Image(),t=document.createElement("a");t.download="category-distribution-chart.png",t.href=e,t.click()}},className:"p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded",title:"Export chart as PNG",children:(0,r.jsx)(c.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>{if(!m)return;let e=new Blob([["Category,Hours,Color",...m.labels.map((e,t)=>({Category:e,Hours:m.datasets[0].data[t],Color:m.datasets[0].borderColor[t]})).map(e=>[e.Category,e.Hours,e.Color].join(","))].join("\n")],{type:"text/csv"}),t=URL.createObjectURL(e),s=document.createElement("a");s.download="category-distribution-data.csv",s.href=t,s.click(),URL.revokeObjectURL(t)},className:"px-2 py-1 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded",title:"Export data as CSV",children:"CSV"})]}),u&&n&&(0,r.jsx)("button",{onClick:()=>{o.forEach(e=>n(e))},className:"text-xs text-gray-500 hover:text-gray-700 underline",children:"Clear filters"})]})]}),(0,r.jsx)("div",{className:"transition-all duration-300",style:{height:i<300?"200px":i<400?"240px":"280px"},children:(0,r.jsx)(F.Fq,{ref:d,data:m,options:x})})]})}function L(e){let{timeEntries:t,className:s="",containerWidth:n=350}=e,i=(0,a.useRef)(null),o=(0,a.useMemo)(()=>{let e={q1:{count:0,label:"Q1: Do First (Important + Urgent)",symbol:"\uD83D\uDD25",color:"#ef4444"},q2:{count:0,label:"Q2: Schedule (Important)",symbol:"⭐",color:"#10b981"},q3:{count:0,label:"Q3: Delegate (Urgent)",symbol:"⚡",color:"#f59e0b"},q4:{count:0,label:"Q4: Eliminate (Neither)",symbol:"\uD83D\uDCA4",color:"#6b7280"}};if(t.forEach(t=>{t.isImportant&&t.isUrgent?e.q1.count++:t.isImportant&&!t.isUrgent?e.q2.count++:!t.isImportant&&t.isUrgent?e.q3.count++:e.q4.count++}),0===t.length)return null;let s=Object.values(e).filter(e=>e.count>0);return{labels:s.map(e=>"".concat(e.symbol," ").concat(e.label)),datasets:[{data:s.map(e=>e.count),backgroundColor:s.map(e=>e.color+"80"),borderColor:s.map(e=>e.color),borderWidth:2,hoverBackgroundColor:s.map(e=>e.color),hoverBorderWidth:3}]}},[t]),d=(0,a.useMemo)(()=>({responsive:!0,maintainAspectRatio:!1,animation:{duration:n<350?0:750},plugins:{legend:{position:"bottom",align:"center",labels:{usePointStyle:!0,font:{size:n<350?11:12,family:"system-ui"},boxWidth:n<350?30:40,padding:n<350?12:20},maxWidth:n-40},tooltip:{callbacks:{label:function(e){let t=e.label||"",s=e.parsed,r=(s/e.dataset.data.reduce((e,t)=>e+t,0)*100).toFixed(1);return"".concat(t,": ").concat(s,"h (").concat(r,"%)")}},padding:n<350?8:12,bodyFont:{size:n<350?11:13}}}}),[n]);return o?(0,r.jsx)("div",{className:(0,l.cn)("bg-white p-4 rounded-lg shadow",s),children:(0,r.jsxs)("div",{className:(0,l.cn)("space-y-4",n<350&&"space-y-3"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between flex-wrap gap-2",children:[(0,r.jsx)("h3",{className:(0,l.cn)("font-medium text-gray-900",n<350?"text-xs":"text-sm"),children:"Priority Distribution"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{className:(0,l.cn)("text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 transition-colors",n<350?"p-1":"p-1.5"),onClick:()=>{if(i.current){let e=i.current.toBase64Image(),t=document.createElement("a");t.download="priority-distribution-chart.png",t.href=e,t.click()}},title:"Export chart as PNG",children:(0,r.jsx)(c.A,{className:(0,l.cn)("w-4 h-4",n<350&&"w-3.5 h-3.5")})}),(0,r.jsx)("button",{className:(0,l.cn)("text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 transition-colors",n<350?"p-1":"p-1.5"),onClick:()=>{if(!(null==o?void 0:o.labels))return;let e=o.datasets[0].data,t=e.reduce((e,t)=>e+t,0),s=new Blob([["Quadrant,Hours,Percentage",...o.labels.map((s,r)=>({Quadrant:s,Hours:e[r],Percentage:(e[r]/t*100).toFixed(1)})).map(e=>[e.Quadrant,e.Hours,e.Percentage].join(","))].join("\n")],{type:"text/csv"}),r=URL.createObjectURL(s),a=document.createElement("a");a.download="priority-distribution-data.csv",a.href=r,a.click(),URL.revokeObjectURL(r)},title:"Export data as CSV",children:"CSV"})]})]}),(0,r.jsx)("div",{style:{height:n<350?"200px":n<450?"240px":"280px"},children:(0,r.jsx)(F.Fq,{ref:i,data:o,options:d})})]})}):(0,r.jsx)("div",{className:(0,l.cn)("flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300",n<350?"h-48":"h-64",s),children:(0,r.jsxs)("div",{className:"text-center px-4",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-2",children:"\uD83D\uDCCA"}),(0,r.jsx)("p",{className:(0,l.cn)("text-gray-500 mb-1",n<350?"text-xs":"text-sm"),children:"No time entries yet"}),(0,r.jsx)("p",{className:(0,l.cn)("text-gray-400",n<350?"text-[10px]":"text-xs"),children:"Add some time entries to see the priority distribution"})]})})}U.t1.register(U.Bs,U.m_,U.s$),U.t1.register(U.Bs,U.m_,U.s$);var R=s(2381);function O(e){var t;let{timeEntries:s,currentWeek:n,className:i="",containerWidth:o=320}=e,d=(0,a.useMemo)(()=>{let e=(0,m.k)(n,{weekStartsOn:1}),t={},r=0;for(let a=0;a<7;a++){let n=(0,R.f)(e,a),i=(0,l.bU)(n),o=s.filter(e=>e.date===i);t[i]=o.length,r+=o.length}let a=Math.round(r/98*100);return{dailyHours:t,totalHours:r,totalPossibleHours:98,completionPercentage:a,weekStart:e}},[s,n]),x=e=>(0,l.uA)(e);return(0,r.jsx)("div",{className:(0,l.cn)("bg-white p-4 rounded-lg shadow",i),children:(0,r.jsxs)("div",{className:(0,l.cn)("space-y-4",o<350&&"space-y-3"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between flex-wrap gap-2",children:[(0,r.jsx)("h3",{className:(0,l.cn)("font-medium text-gray-900",o<350?"text-xs":"text-sm"),children:"Weekly Progress"}),(0,r.jsx)("button",{className:(0,l.cn)("p-1.5 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 transition-colors",o<350&&"p-1"),onClick:()=>{let e=new Blob([["Date,Day,Hours,MaxHours,Percentage",...Array.from({length:7},(e,t)=>{let s=(0,R.f)(d.weekStart,t),r=(0,l.bU)(s),a=d.dailyHours[r]||0;return{Date:r,Day:x(s),Hours:a,MaxHours:14,Percentage:(a/14*100).toFixed(1)}}).map(e=>[e.Date,e.Day,e.Hours,e.MaxHours,e.Percentage].join(","))].join("\n")],{type:"text/csv"}),t=URL.createObjectURL(e),s=document.createElement("a");s.download="weekly-progress-data.csv",s.href=t,s.click(),URL.revokeObjectURL(t)},title:"Export data as CSV",children:(0,r.jsx)(c.A,{className:(0,l.cn)("w-4 h-4",o<350&&"w-3.5 h-3.5")})})]}),(0,r.jsxs)("div",{className:(0,l.cn)("mb-6",o<350&&"mb-4"),children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:(0,l.cn)("text-gray-600",o<350?"text-xs":"text-sm"),children:"Overall Completion"}),(0,r.jsx)("span",{className:(0,l.cn)("font-medium text-gray-900",o<350?"text-xs":"text-sm"),children:"".concat(d.totalHours,"h / ").concat(d.totalPossibleHours,"h (").concat(d.completionPercentage,"%)")})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,r.jsx)("div",{className:(0,l.cn)("rounded-full transition-all duration-300 h-2.5",(t=d.completionPercentage)>=80?"bg-green-500":t>=60?"bg-yellow-500":t>=40?"bg-orange-500":"bg-red-500"),style:{width:"".concat(Math.min(d.completionPercentage,100),"%")}})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:(0,l.cn)("font-medium text-gray-700 mb-3",o<350?"text-xs mb-2":"text-sm"),children:"Daily Breakdown"}),(0,r.jsx)("div",{className:"grid grid-cols-7 gap-1.5",children:Array.from({length:7},(e,t)=>{let s=(0,R.f)(d.weekStart,t),a=(0,l.bU)(s),n=d.dailyHours[a]||0,i=n/14*100;return(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:(0,l.cn)("text-gray-500 mb-1",o<350?"text-[10px]":"text-xs"),children:x(s)}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:(0,l.cn)("w-full bg-gray-200 rounded flex items-end",o<350?"h-12":"h-16"),children:(0,r.jsx)("div",{className:(0,l.cn)("w-full rounded transition-all duration-300",(e=>{let t=e/14*100;return t>=80?"bg-green-400":t>=60?"bg-yellow-400":t>=40?"bg-orange-400":t>0?"bg-red-400":"bg-gray-200"})(n)),style:{height:"".concat(Math.max(i,5),"%")}})}),(0,r.jsx)("div",{className:(0,l.cn)("text-gray-600 mt-1",o<350?"text-[10px]":"text-xs"),children:"".concat(n,"h")})]})]},a)})})]}),(0,r.jsx)("div",{className:(0,l.cn)("mt-4 pt-4 border-t border-gray-100",o<350&&"mt-3 pt-3"),children:(0,r.jsxs)("div",{className:(0,l.cn)("flex items-center justify-between text-gray-500",o<350?"text-[10px]":"text-xs"),children:[(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2 md:gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("div",{className:"w-2.5 h-2.5 bg-green-400 rounded"}),(0,r.jsx)("span",{children:"80%+"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("div",{className:"w-2.5 h-2.5 bg-yellow-400 rounded"}),(0,r.jsx)("span",{children:"60-79%"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("div",{className:"w-2.5 h-2.5 bg-orange-400 rounded"}),(0,r.jsx)("span",{children:"40-59%"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("div",{className:"w-2.5 h-2.5 bg-red-400 rounded"}),(0,r.jsx)("span",{children:"<40%"})]})]}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Daily completion rate"})]})})]})})}var P=s(7828),z=s(532);function M(e){let{categories:t,selectedCategories:s,onToggleCategory:a,onSelectAll:l,onSelectNone:n,className:i=""}=e,o=0===s.length,c=t.length>0&&s.length===t.length,d=!o&&!c,m=()=>{l()};return(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-4 ".concat(i),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Category Visibility"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{onClick:m,disabled:o,className:"text-xs text-blue-600 hover:text-blue-800 disabled:text-gray-400 disabled:cursor-not-allowed",children:"Show All"}),(0,r.jsx)("span",{className:"text-xs text-gray-300",children:"|"}),(0,r.jsx)("button",{onClick:()=>{n()},disabled:c,className:"text-xs text-blue-600 hover:text-blue-800 disabled:text-gray-400 disabled:cursor-not-allowed",children:"Hide All"})]})]}),0===t.length?(0,r.jsx)("p",{className:"text-xs text-gray-500 text-center py-4",children:"No categories available"}):(0,r.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:t.map(e=>{let t,l=(t=e.id,!s.includes(t));return(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 md:p-2 rounded hover:bg-gray-50 transition-colors touch-manipulation",children:[(0,r.jsx)("button",{onClick:()=>a(e.id),className:"flex items-center justify-center w-6 h-6 md:w-5 md:h-5 rounded border-2 transition-colors min-w-[44px] min-h-[44px] md:min-w-0 md:min-h-0 ".concat(l?"bg-blue-50 border-blue-500 text-blue-600":"bg-gray-50 border-gray-300 text-gray-400"),children:l?(0,r.jsx)(P.A,{className:"w-4 h-4 md:w-3 md:h-3"}):(0,r.jsx)(z.A,{className:"w-4 h-4 md:w-3 md:h-3"})}),(0,r.jsx)("div",{className:"w-4 h-4 md:w-3 md:h-3 rounded-full border border-gray-300 flex-shrink-0",style:{backgroundColor:e.color}}),(0,r.jsx)("span",{className:"text-sm md:text-sm flex-1 transition-colors ".concat(l?"text-gray-900":"text-gray-400"),children:e.name}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:l?"Visible":"Hidden"})]},e.id)})}),(0,r.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,r.jsxs)("span",{className:"text-gray-600",children:[o&&"All categories visible",d&&"".concat(t.length-s.length," of ").concat(t.length," visible"),c&&"No categories visible"]}),(d||c)&&(0,r.jsx)("button",{onClick:m,className:"text-blue-600 hover:text-blue-800 underline",children:"Reset"})]})})]})}var _=s(5229);function H(e){let{startDate:t,endDate:s,onDateRangeChange:l,onClear:n,className:i=""}=e,[o,c]=(0,a.useState)(!1),[d,m]=(0,a.useState)((null==t?void 0:t.toISOString().split("T")[0])||""),[x,u]=(0,a.useState)((null==s?void 0:s.toISOString().split("T")[0])||""),g=t&&s,y=()=>{m(""),u(""),n(),c(!1)};return(0,r.jsxs)("div",{className:"relative ".concat(i),children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("button",{onClick:()=>c(!o),className:"flex items-center gap-2 px-3 py-2 text-sm border rounded-md transition-colors ".concat(g?"bg-blue-50 border-blue-200 text-blue-700":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"," ").concat(g?"rounded-r-none border-r-0":""),children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),g?(0,r.jsxs)("span",{children:[null==t?void 0:t.toLocaleDateString()," - ",null==s?void 0:s.toLocaleDateString()]}):(0,r.jsx)("span",{children:"Select date range"})]}),g&&(0,r.jsx)("button",{onClick:y,className:"px-2 py-2 text-sm bg-blue-50 border border-blue-200 border-l-0 text-blue-700 hover:bg-blue-100 rounded-r-md",children:(0,r.jsx)(_.A,{className:"w-3 h-3"})})]}),o&&(0,r.jsx)("div",{className:"absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 p-4 min-w-[300px]",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"start-date",className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),(0,r.jsx)("input",{id:"start-date",type:"date",value:d,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"end-date",className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),(0,r.jsx)("input",{id:"end-date",type:"date",value:x,onChange:e=>u(e.target.value),min:d,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quick Select"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsx)("button",{onClick:()=>{let e=new Date,t=new Date(e);t.setDate(e.getDate()-7),m(t.toISOString().split("T")[0]),u(e.toISOString().split("T")[0])},className:"px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded",children:"Last 7 days"}),(0,r.jsx)("button",{onClick:()=>{let e=new Date,t=new Date(e);t.setDate(e.getDate()-30),m(t.toISOString().split("T")[0]),u(e.toISOString().split("T")[0])},className:"px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded",children:"Last 30 days"}),(0,r.jsx)("button",{onClick:()=>{let e=new Date;m(new Date(e.getFullYear(),e.getMonth(),1).toISOString().split("T")[0]),u(e.toISOString().split("T")[0])},className:"px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded",children:"This month"}),(0,r.jsx)("button",{onClick:()=>{let e=new Date,t=new Date(e.getFullYear(),e.getMonth()-1,1),s=new Date(e.getFullYear(),e.getMonth(),0);m(t.toISOString().split("T")[0]),u(s.toISOString().split("T")[0])},className:"px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded",children:"Last month"})]})]}),(0,r.jsxs)("div",{className:"flex justify-between pt-2 border-t border-gray-200",children:[(0,r.jsx)("button",{onClick:y,className:"px-3 py-1 text-sm text-gray-600 hover:text-gray-800",children:"Clear"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{onClick:()=>{m((null==t?void 0:t.toISOString().split("T")[0])||""),u((null==s?void 0:s.toISOString().split("T")[0])||""),c(!1)},className:"px-3 py-1 text-sm text-gray-600 hover:text-gray-800",children:"Cancel"}),(0,r.jsx)("button",{onClick:()=>{if(d&&x){let e=new Date(d),t=new Date(x);e<=t&&(l(e,t),c(!1))}},disabled:!d||!x,className:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Apply"})]})]})]})}),o&&(0,r.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>c(!1)})]})}var Q=s(9476);function B(e){let{children:t,defaultWidth:s=350,minWidth:n=250,maxWidth:i=500,className:o,onResize:c,storageKey:d="resizable-panel-width"}=e,[m,x]=(0,a.useState)(s),[u,g]=(0,a.useState)(!1),h=(0,a.useRef)(null),y=(0,a.useRef)(0),p=(0,a.useRef)(0);(0,a.useEffect)(()=>{{let e=localStorage.getItem(d);if(e){let t=parseInt(e,10);t>=n&&t<=i&&x(t)}}},[d,n,i]),(0,a.useEffect)(()=>{localStorage.setItem(d,m.toString()),null==c||c(m)},[m,d,c]);let b=(0,a.useCallback)(e=>{e.preventDefault(),y.current=e.clientX,p.current=m,g(!0),document.body.style.cursor="col-resize",document.body.style.userSelect="none",document.body.classList.add("resize-active")},[m]);return(0,a.useEffect)(()=>{if(!u)return;let e=e=>{e.preventDefault();let t=e.clientX-y.current;x(Math.max(n,Math.min(i,p.current+t)))},t=()=>{g(!1),document.body.style.cursor="",document.body.style.userSelect="",document.body.classList.remove("resize-active")};return document.addEventListener("mousemove",e),document.addEventListener("mouseup",t),()=>{document.removeEventListener("mousemove",e),document.removeEventListener("mouseup",t)}},[u,n,i]),(0,r.jsx)("div",{className:"flex h-full",children:(0,r.jsxs)("div",{ref:h,className:(0,l.cn)("relative bg-white border-r border-gray-200 h-full overflow-hidden",!u&&"transition-all duration-300 ease-in-out",o),style:{width:"".concat(m,"px")},children:[(0,r.jsx)("div",{className:"h-full overflow-y-auto custom-scrollbar",children:t}),(0,r.jsx)("div",{role:"separator","aria-label":"Resize panel","aria-valuenow":m,"aria-valuemin":n,"aria-valuemax":i,className:(0,l.cn)("absolute top-0 right-0 w-4 h-full cursor-col-resize group transition-colors z-20",'after:content-[""] after:absolute after:top-0 after:left-1/2 after:w-px after:h-full',"after:bg-gray-300 after:transition-opacity after:duration-200",u?"after:opacity-100":"after:opacity-60 hover:after:opacity-100"),onMouseDown:b,children:(0,r.jsx)("div",{className:(0,l.cn)("absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-1/2","transition-all duration-200",u&&"scale-110"),children:(0,r.jsx)("div",{className:(0,l.cn)("flex items-center justify-center w-6 h-16 bg-white border border-gray-300 rounded-md","shadow-md hover:shadow-lg transition-all duration-200","opacity-80 group-hover:opacity-100",u&&"opacity-100 shadow-xl border-blue-500 bg-blue-50"),children:(0,r.jsx)(Q.A,{className:(0,l.cn)("h-4 w-4 transition-colors duration-200",u?"text-primary-500":"text-gray-400 group-hover:text-gray-500")})})})})]})})}function G(){var e,t;let{state:s,actions:n}=(0,o.A)(),{activeTab:i}=D(),c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"resizable-panel-width",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:320,[s,r]=(0,a.useState)(t);return(0,a.useEffect)(()=>{{let t=localStorage.getItem(e);t&&r(parseInt(t,10))}let t=t=>{t.key===e&&t.newValue&&r(parseInt(t.newValue,10))};return window.addEventListener("storage",t),()=>window.removeEventListener("storage",t)},[e]),s}("stats"===i?"stats-panel-width":"sidebar-width","stats"===i?450:320),d=(0,a.useMemo)(()=>{let e=(0,l._2)(s.currentWeek).map(l.bU),t=s.timeEntries.filter(t=>e.includes(t.date)),{selectedCategories:r,dateRange:a}=s.chartFilters;return r.length>0&&(t=t.filter(e=>r.includes(e.categoryId))),a&&(t=t.filter(e=>{let t=new Date(e.date);return t>=a.startDate&&t<=a.endDate})),t},[s.timeEntries,s.currentWeek,s.chartFilters]),m=(0,a.useMemo)(()=>{var e;let t=(0,l._2)(s.currentWeek).map(l.bU),r=s.timeEntries.filter(e=>t.includes(e.date)),a=r.length,n=(0,l.$4)(a,w.r1),i=s.categories.map(e=>{let t=r.filter(t=>t.categoryId===e.id);return{category:e,hours:t.length,percentage:(0,l.$4)(t.length,a)}}).filter(e=>e.hours>0).sort((e,t)=>t.hours-e.hours),o={[w.eW.IMPORTANT_URGENT]:r.filter(e=>e.isImportant&&e.isUrgent).length,[w.eW.IMPORTANT_NOT_URGENT]:r.filter(e=>e.isImportant&&!e.isUrgent).length,[w.eW.NOT_IMPORTANT_URGENT]:r.filter(e=>!e.isImportant&&e.isUrgent).length,[w.eW.NOT_IMPORTANT_NOT_URGENT]:r.filter(e=>!e.isImportant&&!e.isUrgent).length},c=null==(e=i[0])?void 0:e.category;return{totalTracked:a,trackingPercentage:n,categoryStats:i,quadrantStats:o,mostUsedCategory:c}},[s.timeEntries,s.categories,s.currentWeek]);return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"card p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 text-primary-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"This Week"})]}),(0,r.jsxs)("span",{className:"text-lg font-bold text-primary-600",children:[m.trackingPercentage,"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(m.trackingPercentage,"%")}})}),(0,r.jsxs)("p",{className:"text-xs text-gray-600 mt-1",children:[m.totalTracked," of ",w.r1," hours tracked"]})]}),m.mostUsedCategory&&(0,r.jsxs)("div",{className:"card p-4",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(E.A,{className:"h-4 w-4 text-green-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Top Category"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"category-color-dot",style:{backgroundColor:m.mostUsedCategory.color}}),(0,r.jsx)("span",{className:"text-sm text-gray-900",children:m.mostUsedCategory.name})]}),(0,r.jsxs)("p",{className:"text-xs text-gray-600 mt-1",children:[m.categoryStats[0].hours," hours (",m.categoryStats[0].percentage,"%)"]})]}),(0,r.jsxs)("div",{className:"card p-4",children:[(0,r.jsxs)("div",{className:"flex items-center mb-3",children:[(0,r.jsx)(T.A,{className:"h-4 w-4 text-blue-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Priority Matrix"})]}),(0,r.jsx)("div",{className:"space-y-2",children:Object.entries(m.quadrantStats).map(e=>{let[t,s]=e,a=(e=>{switch(e){case w.eW.IMPORTANT_URGENT:return{symbol:"\uD83D\uDD25",label:"Do First",color:"text-red-600 bg-red-50"};case w.eW.IMPORTANT_NOT_URGENT:return{symbol:"⭐",label:"Schedule",color:"text-green-600 bg-green-50"};case w.eW.NOT_IMPORTANT_URGENT:return{symbol:"⚡",label:"Delegate",color:"text-yellow-600 bg-yellow-50"};case w.eW.NOT_IMPORTANT_NOT_URGENT:return{symbol:"\uD83D\uDCA4",label:"Eliminate",color:"text-gray-600 bg-gray-50"}}})(t);return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-sm mr-2",children:a.symbol}),(0,r.jsx)("span",{className:"text-xs text-gray-700",children:a.label})]}),(0,r.jsxs)("span",{className:"text-xs font-medium text-gray-900",children:[s,"h"]})]},t)})})]}),m.categoryStats.length>0&&(0,r.jsxs)("div",{className:"card p-4",children:[(0,r.jsxs)("div",{className:"flex items-center mb-3",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 text-purple-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Categories"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[m.categoryStats.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"category-color-dot flex-shrink-0",style:{backgroundColor:e.category.color}}),(0,r.jsx)("span",{className:"text-xs text-gray-700 truncate",children:e.category.name})]}),(0,r.jsxs)("span",{className:"text-xs font-medium text-gray-900 ml-2",children:[e.hours,"h"]})]},e.category.id)),m.categoryStats.length>5&&(0,r.jsxs)("p",{className:"text-xs text-gray-500 text-center pt-1",children:["+",m.categoryStats.length-5," more"]})]})]}),(0,r.jsxs)("div",{className:"card p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Chart Filters"}),(s.chartFilters.selectedCategories.length>0||s.chartFilters.dateRange)&&(0,r.jsx)("button",{onClick:n.clearAllFilters,className:"text-xs text-gray-500 hover:text-gray-700 underline",children:"Clear all filters"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-2",children:"Date Range Filter"}),(0,r.jsx)(H,{startDate:null==(e=s.chartFilters.dateRange)?void 0:e.startDate,endDate:null==(t=s.chartFilters.dateRange)?void 0:t.endDate,onDateRangeChange:n.setDateRangeFilter,onClear:n.clearDateRangeFilter})]}),(0,r.jsx)("div",{children:(0,r.jsx)(M,{categories:s.categories,selectedCategories:s.chartFilters.selectedCategories,onToggleCategory:n.toggleCategoryFilter,onSelectAll:n.clearAllFilters,onSelectNone:n.hideAllCategories})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(O,{timeEntries:d,currentWeek:s.currentWeek,containerWidth:c}),(0,r.jsx)(W,{timeEntries:d,categories:s.categories,onCategoryClick:n.toggleCategoryFilter,selectedCategories:s.chartFilters.selectedCategories,containerWidth:c}),(0,r.jsx)(L,{timeEntries:d,containerWidth:c})]})]})}function q(e){return 0===e.length}async function K(e,t,s,r){if(q(e))for(let e of function(e,t){let s=(0,l._2)(t),r=[];return[{day:0,hour:9,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Sprint planning meeting"},{day:0,hour:10,categoryName:"Work/Coding",important:!0,urgent:!0,description:"Fix critical bug"},{day:0,hour:11,categoryName:"Work/Coding",important:!0,urgent:!0,description:"Code review"},{day:0,hour:14,categoryName:"Learning/Study",important:!0,urgent:!1,description:"AI LangGraph tutorial"},{day:0,hour:15,categoryName:"Learning/Study",important:!0,urgent:!1,description:"Practice coding"},{day:0,hour:19,categoryName:"Exercise/Health",important:!0,urgent:!1,description:"Gym workout"},{day:0,hour:21,categoryName:"Rest/Entertainment",important:!1,urgent:!1,description:"Watch Netflix"},{day:1,hour:9,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Feature development"},{day:1,hour:10,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Testing"},{day:1,hour:11,categoryName:"Work/Coding",important:!1,urgent:!0,description:"Team standup"},{day:1,hour:14,categoryName:"Learning/Study",important:!0,urgent:!1,description:"Read documentation"},{day:1,hour:18,categoryName:"Family/Social",important:!0,urgent:!1,description:"Dinner with family"},{day:1,hour:20,categoryName:"Exercise/Health",important:!0,urgent:!1,description:"Evening walk"},{day:2,hour:9,categoryName:"Work/Coding",important:!0,urgent:!0,description:"Client presentation"},{day:2,hour:10,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Documentation"},{day:2,hour:11,categoryName:"Work/Coding",important:!1,urgent:!0,description:"Email responses"},{day:2,hour:15,categoryName:"Learning/Study",important:!0,urgent:!1,description:"Online course"},{day:2,hour:19,categoryName:"Family/Social",important:!0,urgent:!1,description:"Call parents"},{day:3,hour:9,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Architecture design"},{day:3,hour:10,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Implementation"},{day:3,hour:14,categoryName:"Learning/Study",important:!0,urgent:!1,description:"Research new tech"},{day:3,hour:18,categoryName:"Exercise/Health",important:!0,urgent:!1,description:"Yoga session"},{day:3,hour:20,categoryName:"Rest/Entertainment",important:!1,urgent:!1,description:"Read book"},{day:4,hour:9,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Code cleanup"},{day:4,hour:10,categoryName:"Work/Coding",important:!1,urgent:!0,description:"Weekly report"},{day:4,hour:11,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Team retrospective"},{day:4,hour:15,categoryName:"Learning/Study",important:!0,urgent:!1,description:"Skill practice"},{day:4,hour:19,categoryName:"Family/Social",important:!0,urgent:!1,description:"Friends meetup"},{day:5,hour:10,categoryName:"Exercise/Health",important:!0,urgent:!1,description:"Morning run"},{day:5,hour:14,categoryName:"Family/Social",important:!0,urgent:!1,description:"Family time"},{day:5,hour:16,categoryName:"Rest/Entertainment",important:!1,urgent:!1,description:"Hobby project"},{day:5,hour:20,categoryName:"Rest/Entertainment",important:!1,urgent:!1,description:"Movie night"},{day:6,hour:11,categoryName:"Exercise/Health",important:!0,urgent:!1,description:"Outdoor activity"},{day:6,hour:14,categoryName:"Learning/Study",important:!0,urgent:!1,description:"Personal project"},{day:6,hour:16,categoryName:"Family/Social",important:!0,urgent:!1,description:"Social gathering"},{day:6,hour:19,categoryName:"Rest/Entertainment",important:!1,urgent:!1,description:"Relax time"}].forEach(t=>{let a=e.find(e=>e.name===t.categoryName);if(a&&s[t.day]){let e={id:"sample-".concat(t.day,"-").concat(t.hour),date:(0,l.bU)(s[t.day]),hour:t.hour,categoryId:a.id,isImportant:t.important,isUrgent:t.urgent,description:t.description,createdAt:new Date,updatedAt:new Date};r.push(e)}}),r}(t,s))try{await r(e.date,e.hour,{categoryId:e.categoryId,isImportant:e.isImportant,isUrgent:e.isUrgent,description:e.description})}catch(e){console.warn("Failed to add sample entry:",e)}}var V=s(7726),Y=s(534),$=s(6191),J=s(5710);function X(){let{state:e,actions:t}=(0,o.A)(),{activeTab:s,setActiveTab:l}=D(),[n,c]=(0,a.useState)(!1),[d,m]=(0,a.useState)(!1),[x,u]=(0,a.useState)(!1),g=async()=>{m(!0);try{await K(e.timeEntries,e.categories,e.currentWeek,t.upsertTimeEntry)}catch(e){console.error("Failed to load sample data:",e)}finally{m(!1)}},h=async()=>{if(confirm("This will replace ALL existing categories with 8 predefined default categories. This action cannot be undone. Continue?")){u(!0);try{await t.loadDefaultCategories()}catch(e){console.error("Failed to load default categories:",e)}finally{u(!1)}}};return(0,r.jsx)("aside",{className:"w-full h-full bg-white overflow-y-auto custom-scrollbar",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex space-x-1 mb-6",children:[(0,r.jsxs)("button",{onClick:()=>l("categories"),className:"flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ".concat("categories"===s?"bg-primary-100 text-primary-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:[(0,r.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"Categories"]}),(0,r.jsxs)("button",{onClick:()=>l("stats"),className:"flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ".concat("stats"===s?"bg-primary-100 text-primary-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:[(0,r.jsx)(Y.A,{className:"h-4 w-4 mr-2"}),"Stats"]})]}),"categories"===s&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(i,{onClick:()=>c(!0),className:"w-full",size:"sm",children:[(0,r.jsx)($.A,{className:"h-4 w-4 mr-2"}),"Add Category"]}),(0,r.jsxs)(i,{onClick:h,variant:"secondary",size:"sm",isLoading:x,className:"w-full",children:[(0,r.jsx)(J.A,{className:"h-4 w-4 mr-2"}),x?"Loading...":"Load Default Categories"]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 text-center",children:"Replaces all categories with 8 predefined ones"})]}),n&&(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 bg-gray-50",children:(0,r.jsx)(k,{onSuccess:()=>c(!1),onCancel:()=>c(!1)})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:["Categories (",e.categories.length,")"]}),(0,r.jsx)(S,{})]})]}),"stats"===s&&(0,r.jsxs)("div",{className:"space-y-6",children:[q(e.timeEntries)&&(0,r.jsxs)("div",{children:[(0,r.jsxs)(i,{onClick:g,variant:"secondary",size:"sm",isLoading:d,className:"w-full",children:[(0,r.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"Load Sample Data"]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2 text-center",children:"Add sample time entries to see charts in action"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Quick Stats"}),(0,r.jsx)(G,{})]})]})]})})}function Z(e){var t;let{selectedSlots:s,onClose:l}=e,{state:n,actions:c}=(0,o.A)(),[d,m]=(0,a.useState)(!1),[x,u]=(0,a.useState)({categoryId:(null==(t=n.categories[0])?void 0:t.id)||"",isImportant:!1,isUrgent:!1,description:""}),g=async e=>{if(e.preventDefault(),x.categoryId){m(!0);try{await c.batchCreatePlannedEntries(s,x),l()}catch(e){console.error("Failed to batch create planned entries:",e)}finally{m(!1)}}};return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Batch Plan for ",s.length," Slots"]})}),(0,r.jsx)("button",{onClick:l,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(_.A,{className:"h-5 w-5"})})]}),(0,r.jsxs)("form",{onSubmit:g,className:"p-6 space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:"Category *"}),(0,r.jsxs)("select",{id:"category",value:x.categoryId,onChange:e=>u({...x,categoryId:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,r.jsx)("option",{value:"",children:"Select a category"}),n.categories.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description (optional)"}),(0,r.jsx)("input",{type:"text",id:"description",value:x.description,onChange:e=>u({...x,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"What do you plan to do?"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"important",checked:x.isImportant,onChange:e=>u({...x,isImportant:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"important",className:"ml-2 block text-sm text-gray-900",children:"Important"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"urgent",checked:x.isUrgent,onChange:e=>u({...x,isUrgent:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"urgent",className:"ml-2 block text-sm text-gray-900",children:"Urgent"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)(i,{type:"submit",isLoading:d,className:"flex-1",disabled:!x.categoryId,children:"Apply to All"}),(0,r.jsx)(i,{type:"button",variant:"secondary",onClick:l,disabled:d,children:"Cancel"})]})]})]})})}function ee(e){let{value:t,onChange:s,categories:l}=e,[n,i]=(0,a.useState)(!1),[o,c]=(0,a.useState)(!1),d=l.find(e=>e.id===t);return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>i(!n),className:"w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-lg bg-white hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",children:[(0,r.jsx)("div",{className:"flex items-center",children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"category-color-dot",style:{backgroundColor:d.color}}),(0,r.jsx)("span",{className:"text-sm text-gray-900",children:d.name})]}):(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Select a category"})}),(0,r.jsx)(y.A,{className:"h-4 w-4 text-gray-400 transition-transform ".concat(n?"rotate-180":"")})]}),n&&(0,r.jsxs)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:[(0,r.jsx)("div",{className:"py-1",children:l.map(e=>(0,r.jsxs)("button",{type:"button",onClick:()=>{s(e.id),i(!1)},className:"w-full flex items-center px-3 py-2 text-left hover:bg-gray-50 transition-colors ".concat(t===e.id?"bg-primary-50 text-primary-700":"text-gray-900"),children:[(0,r.jsx)("div",{className:"category-color-dot",style:{backgroundColor:e.color}}),(0,r.jsx)("span",{className:"text-sm",children:e.name})]},e.id))}),(0,r.jsx)("div",{className:"border-t border-gray-200 py-1",children:(0,r.jsxs)("button",{type:"button",onClick:()=>{c(!0),i(!1)},className:"w-full flex items-center px-3 py-2 text-left text-primary-600 hover:bg-primary-50 transition-colors",children:[(0,r.jsx)($.A,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Add New Category"})]})})]}),n&&(0,r.jsx)("div",{className:"fixed inset-0 z-5",onClick:()=>i(!1)}),o&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-xl max-w-sm w-full p-6",children:(0,r.jsx)(k,{onSuccess:()=>{c(!1)},onCancel:()=>c(!1)})})})]})}function et(e){var t;let{selectedSlots:s,onClose:l}=e,{state:n,actions:c}=(0,o.A)(),[d,m]=(0,a.useState)(!1),[x,u]=(0,a.useState)({categoryId:(null==(t=n.categories[0])?void 0:t.id)||"",isImportant:!1,isUrgent:!1,description:""}),g=async e=>{if(e.preventDefault(),x.categoryId){m(!0);try{await c.batchUpsertTimeEntries(s,x),l()}catch(e){console.error("Failed to batch create time entries:",e)}finally{m(!1)}}};return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Batch Track for ",s.length," Slots"]})}),(0,r.jsx)("button",{onClick:l,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(_.A,{className:"h-5 w-5"})})]}),(0,r.jsxs)("form",{onSubmit:g,className:"p-6 space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category *"}),(0,r.jsx)(ee,{value:x.categoryId,onChange:e=>u({...x,categoryId:e}),categories:n.categories})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Eisenhower Matrix"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:x.isImportant,onChange:e=>u({...x,isImportant:e.target.checked}),className:"checkbox-field"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Important"})]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:x.isUrgent,onChange:e=>u({...x,isUrgent:e.target.checked}),className:"checkbox-field"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Urgent"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description (optional)"}),(0,r.jsx)("input",{type:"text",id:"description",value:x.description,onChange:e=>u({...x,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"What did you do?"})]}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)(i,{type:"submit",isLoading:d,className:"flex-1",disabled:!x.categoryId,children:"Apply to All"}),(0,r.jsx)(i,{type:"button",variant:"secondary",onClick:l,disabled:d,children:"Cancel"})]})]})]})})}function es(e){var t;let{date:s,hour:n,onClose:c}=e,{state:d,actions:m}=(0,o.A)(),[x,u]=(0,a.useState)(!1),[g,h]=(0,a.useState)({categoryId:"",isImportant:!1,isUrgent:!1,description:""}),y=null==(t=d.plannedEntries)?void 0:t.find(e=>e.date===s&&e.hour===n);(0,a.useEffect)(()=>{y&&h({categoryId:y.categoryId,isImportant:y.isImportant,isUrgent:y.isUrgent,description:y.description||""})},[y]);let p=async e=>{if(e.preventDefault(),g.categoryId){u(!0);try{y?await m.updatePlannedEntry(y.id,g):await m.createPlannedEntry(s,n,g),c()}catch(e){console.error("Failed to save planned entry:",e)}finally{u(!1)}}},b=async()=>{if(y){u(!0);try{await m.deletePlannedEntry(y.id),c()}catch(e){console.error("Failed to delete planned entry:",e)}finally{u(!1)}}};return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:[y?"Edit":"Plan"," Activity"]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[new Date(s).toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric"})," at ",(0,l.KY)(n)]})]}),(0,r.jsx)("button",{onClick:c,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(_.A,{className:"h-5 w-5"})})]}),(0,r.jsxs)("form",{onSubmit:p,className:"p-6 space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:"Category *"}),(0,r.jsxs)("select",{id:"category",value:g.categoryId,onChange:e=>h({...g,categoryId:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,r.jsx)("option",{value:"",children:"Select a category"}),d.categories.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description (optional)"}),(0,r.jsx)("input",{type:"text",id:"description",value:g.description,onChange:e=>h({...g,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"What do you plan to do?"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"important",checked:g.isImportant,onChange:e=>h({...g,isImportant:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"important",className:"ml-2 block text-sm text-gray-900",children:"Important"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"urgent",checked:g.isUrgent,onChange:e=>h({...g,isUrgent:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"urgent",className:"ml-2 block text-sm text-gray-900",children:"Urgent"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsxs)(i,{type:"submit",isLoading:x,className:"flex-1",disabled:!g.categoryId,children:[y?"Update":"Save"," Plan"]}),y&&(0,r.jsx)(i,{type:"button",variant:"danger",onClick:b,disabled:x,className:"px-3",children:"Delete"}),(0,r.jsx)(i,{type:"button",variant:"secondary",onClick:c,disabled:x,children:"Cancel"})]})]})]})})}function er(e){let{date:t,hour:s,onClose:n}=e,{state:c,actions:d}=(0,o.A)(),[m,x]=(0,a.useState)({categoryId:"",isImportant:!1,isUrgent:!1,description:""}),[u,g]=(0,a.useState)(!1),[h,y]=(0,a.useState)(null),p=c.timeEntries.find(e=>e.date===t&&e.hour===s);(0,a.useEffect)(()=>{p?x({categoryId:p.categoryId,isImportant:p.isImportant,isUrgent:p.isUrgent,description:p.description||""}):c.categories.length>0&&x(e=>({...e,categoryId:c.categories[0].id}))},[p,c.categories]);let b=async e=>{if(e.preventDefault(),!m.categoryId)return void y("Please select a category");g(!0),y(null);try{await d.upsertTimeEntry(t,s,m),n()}catch(e){y(e instanceof Error?e.message:"Failed to save time entry")}finally{g(!1)}},f=async()=>{if(p){g(!0);try{await d.deleteTimeEntry(t,s),n()}catch(e){y(e instanceof Error?e.message:"Failed to delete time entry")}finally{g(!1)}}};return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:[p?"Edit":"Add"," Time Entry"]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[new Date(t).toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric"})," at ",(0,l.KY)(s)]})]}),(0,r.jsx)("button",{onClick:n,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(_.A,{className:"h-5 w-5"})})]}),(0,r.jsxs)("form",{onSubmit:b,className:"p-6 space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category *"}),(0,r.jsx)(ee,{value:m.categoryId,onChange:e=>x({...m,categoryId:e}),categories:c.categories})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Eisenhower Matrix"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:m.isImportant,onChange:e=>x({...m,isImportant:e.target.checked}),className:"checkbox-field"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Important"})]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:m.isUrgent,onChange:e=>x({...m,isUrgent:e.target.checked}),className:"checkbox-field"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Urgent"})]})]}),(0,r.jsxs)("div",{className:"mt-2 text-xs text-gray-500 flex items-center",children:[(0,r.jsx)("span",{className:"mr-1",children:m.isImportant&&m.isUrgent?"\uD83D\uDD25":m.isImportant&&!m.isUrgent?"⭐":!m.isImportant&&m.isUrgent?"⚡":"\uD83D\uDCA4"}),"Quadrant: ",m.isImportant&&m.isUrgent?"Q1 (Do First)":m.isImportant&&!m.isUrgent?"Q2 (Schedule)":!m.isImportant&&m.isUrgent?"Q3 (Delegate)":"Q4 (Eliminate)"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description (optional)"}),(0,r.jsx)("textarea",{id:"description",value:m.description,onChange:e=>x({...m,description:e.target.value}),placeholder:"Brief description of the activity...",className:"input-field resize-none",rows:3,maxLength:200}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[m.description.length,"/200 characters"]})]}),h&&(0,r.jsx)("div",{className:"text-sm text-red-600 bg-red-50 p-3 rounded",children:h}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsxs)(i,{type:"submit",isLoading:u,className:"flex-1",children:[p?"Update":"Save"," Entry"]}),p&&(0,r.jsx)(i,{type:"button",variant:"danger",onClick:f,disabled:u,className:"px-3",children:(0,r.jsx)(N.A,{className:"h-4 w-4"})}),(0,r.jsx)(i,{type:"button",variant:"secondary",onClick:n,disabled:u,children:"Cancel"})]})]})]})})}function ea(e){let t=parseInt(e.slice(1,3),16),s=parseInt(e.slice(3,5),16);return(.299*t+.587*s+.114*parseInt(e.slice(5,7),16))/255>.5?"#000000":"#FFFFFF"}function el(e){let{timeSlot:t,onClick:s,onPlanClick:a,onClearCell:n,isToday:i=!1,showPlanSection:o=!0,isSelected:c}=e,{actualEntry:d,actualCategory:m,plannedEntry:x,plannedCategory:u}=t,g=!!d,h=!!x,y=e=>e?e.isImportant&&e.isUrgent?{symbol:"\uD83D\uDD25",bgColor:"#ef4444",label:"Q1: Do First"}:e.isImportant&&!e.isUrgent?{symbol:"⭐",bgColor:"#10b981",label:"Q2: Schedule"}:!e.isImportant&&e.isUrgent?{symbol:"⚡",bgColor:"#f59e0b",label:"Q3: Delegate"}:{symbol:"\uD83D\uDCA4",bgColor:"#6b7280",label:"Q4: Eliminate"}:null,p=y(d),b=y(x),f=h&&g?x.categoryId===d.categoryId?"completed":"different":h&&!g?"missed":!h&&g?"unplanned":"empty";return o?(0,r.jsxs)("div",{className:(0,l.cn)("time-slot border-r border-gray-200 last:border-r-0 relative transition-all duration-200",i?"border-primary-200":"","completed"===f&&"ring-2 ring-green-300","different"===f&&"ring-2 ring-yellow-300","missed"===f&&"ring-2 ring-red-300","unplanned"===f&&"ring-2 ring-blue-300",c?"ring-2 ring-blue-500 ring-inset z-10":""),children:[(0,r.jsxs)("div",{className:"flex h-full min-h-[60px] overflow-hidden",children:[(0,r.jsx)("div",{className:(0,l.cn)("w-1/2 border-r border-gray-300 relative cursor-pointer transition-all duration-200 hover:bg-gray-50 overflow-hidden",h&&u?"bg-opacity-30":""),style:{backgroundColor:h&&u?"".concat(u.color,"20"):void 0},onClick:e=>null==a?void 0:a(e),children:(0,r.jsxs)("div",{className:"p-1 h-full flex flex-col justify-between overflow-hidden",children:[h&&u?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"text-xs font-medium truncate text-gray-700 overflow-hidden",children:u.name}),x.description&&(0,r.jsx)("div",{className:"text-xs text-gray-600 truncate mt-1 overflow-hidden",children:x.description})]}):(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("span",{className:"text-xs text-gray-400",children:"Plan"})}),b&&(0,r.jsx)("div",{className:"absolute top-0.5 right-0.5 flex items-center justify-center w-4 h-4 rounded-full text-white text-xs font-bold shadow-sm opacity-70",style:{backgroundColor:b.bgColor},title:"Planned: ".concat(b.label),children:(0,r.jsx)("span",{className:"text-xs leading-none",children:b.symbol})})]})}),(0,r.jsx)("div",{className:(0,l.cn)("w-1/2 relative cursor-pointer transition-all duration-200 overflow-hidden",g?"filled":"hover:bg-gray-50"),style:{backgroundColor:g&&m?m.color:void 0,color:g&&m?ea(m.color):void 0},onClick:e=>s(e),children:(0,r.jsxs)("div",{className:"p-1 h-full flex flex-col justify-between overflow-hidden",children:[g&&m?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"text-xs font-medium truncate overflow-hidden",children:m.name}),d.description&&(0,r.jsx)("div",{className:"text-xs opacity-80 truncate mt-1 overflow-hidden",children:d.description})]}):(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("span",{className:"text-xs text-gray-400",children:"Track"})}),p&&(0,r.jsx)("div",{className:"absolute top-0.5 right-0.5 flex items-center justify-center w-4 h-4 rounded-full text-white text-xs font-bold shadow-sm",style:{backgroundColor:p.bgColor},title:"Actual: ".concat(p.label),children:(0,r.jsx)("span",{className:"text-xs leading-none",children:p.symbol})})]})})]}),"empty"!==f&&(0,r.jsxs)("div",{className:"absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white",children:["completed"===f&&(0,r.jsx)("div",{className:"w-full h-full bg-green-500 rounded-full",title:"Plan completed as expected"}),"different"===f&&(0,r.jsx)("div",{className:"w-full h-full bg-yellow-500 rounded-full",title:"Different activity than planned"}),"missed"===f&&(0,r.jsx)("div",{className:"w-full h-full bg-red-500 rounded-full",title:"Planned activity missed"}),"unplanned"===f&&(0,r.jsx)("div",{className:"w-full h-full bg-blue-500 rounded-full",title:"Unplanned activity"})]}),(h||g)&&n&&(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),n()},className:"absolute top-1 left-1 opacity-0 hover:opacity-100 transition-opacity bg-red-500 hover:bg-red-600 text-white rounded-full p-1 shadow-sm z-10",title:"Clear this time slot",children:(0,r.jsx)(_.A,{className:"h-3 w-3"})})]}):(0,r.jsx)("div",{className:(0,l.cn)("time-slot border-r border-gray-200 last:border-r-0 relative cursor-pointer transition-all duration-200",g?"filled":"",i?"border-primary-200":"",c?"ring-2 ring-blue-500 ring-inset z-10":""),style:{backgroundColor:g&&m?m.color:void 0,color:g&&m?ea(m.color):void 0},onClick:e=>s(e),children:(0,r.jsxs)("div",{className:"p-2 h-full min-h-[60px] flex flex-col justify-between",children:[g&&m&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"text-xs font-medium truncate",children:m.name}),d.description&&(0,r.jsx)("div",{className:"text-xs opacity-80 truncate mt-1",children:d.description})]}),p&&(0,r.jsx)("div",{className:"absolute top-1 right-1 flex items-center justify-center w-6 h-6 rounded-full text-white text-xs font-bold shadow-sm",style:{backgroundColor:p.bgColor},title:p.label,children:(0,r.jsx)("span",{className:"text-sm leading-none",children:p.symbol})}),!g&&(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-gray-100 bg-opacity-50",children:(0,r.jsx)("span",{className:"text-xs text-gray-600 font-medium",children:"+ Add"})}),g&&n&&(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),n()},className:"absolute top-1 left-1 opacity-0 hover:opacity-100 transition-opacity bg-red-500 hover:bg-red-600 text-white rounded-full p-1 shadow-sm",title:"Clear this time slot",children:(0,r.jsx)(_.A,{className:"h-3 w-3"})})]})})}function en(){let{state:e,actions:t}=(0,o.A)(),[s,n]=(0,a.useState)(null),[c,d]=(0,a.useState)(null),[m,x]=(0,a.useState)(new Set),[u,g]=(0,a.useState)(null),[h,y]=(0,a.useState)(!1),[p,b]=(0,a.useState)(!1),f=(0,l._2)(e.currentWeek),j=(0,l.F_)(f,e.timeEntries,e.categories,e.plannedEntries),N=Array.from({length:w.Oc.TOTAL},(e,t)=>w.Oc.START+t),v=(e,t)=>"".concat(e,"_").concat(t),k=async(e,s)=>{if(confirm("Are you sure you want to clear this time slot? This action cannot be undone."))try{await t.clearCellData(e,s)}catch(e){console.error("Failed to clear cell data:",e)}},C=e=>{let t=new Set;for(let s=w.Oc.START;s<=w.Oc.END;s++)t.add(v(e,s));x(t)};return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[(0,r.jsxs)("div",{className:"bg-gray-50 border-b border-gray-200 p-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Weekly Time Grid"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Click any time slot to add or edit an activity"})]}),(0,r.jsx)("div",{className:"overflow-x-auto touch-pan-x",children:(0,r.jsxs)("div",{className:"min-w-[800px] md:min-w-0",children:[(0,r.jsxs)("div",{className:"grid grid-cols-8 border-b border-gray-200",children:[(0,r.jsx)("div",{className:"p-2 md:p-3 bg-gray-50 border-r border-gray-200",children:(0,r.jsx)("span",{className:"text-xs font-medium text-gray-500",children:"Time"})}),j.map(e=>(0,r.jsxs)("div",{className:"p-2 md:p-3 text-center border-r border-gray-200 last:border-r-0 ".concat((0,l.cK)(e.dateObj)?"bg-primary-50":"bg-gray-50"),children:[(0,r.jsxs)("div",{className:"text-xs md:text-sm font-medium text-gray-900",children:[(0,r.jsx)("span",{className:"md:hidden",children:e.dayName.slice(0,3)}),(0,r.jsx)("span",{className:"hidden md:inline",children:e.dayName})]}),(0,r.jsx)("div",{className:"text-xs mt-1 ".concat((0,l.cK)(e.dateObj)?"text-primary-600":"text-gray-500"),children:e.dateObj.getDate()}),(0,r.jsxs)("div",{className:"hidden md:flex justify-center gap-2 mt-1",children:[(0,r.jsx)("button",{onClick:()=>{C(e.date),y(!0)},className:"text-xs text-blue-500 hover:underline",children:"Plan"}),(0,r.jsx)("button",{onClick:()=>{C(e.date),b(!0)},className:"text-xs text-green-500 hover:underline",children:"Track"})]})]},e.date))]}),N.map(e=>(0,r.jsxs)("div",{className:"grid grid-cols-8 border-b border-gray-200 last:border-b-0",children:[(0,r.jsx)("div",{className:"p-3 bg-gray-50 border-r border-gray-200 flex items-center",children:(0,r.jsx)("span",{className:"text-xs font-medium text-gray-600",children:(0,l.KY)(e)})}),j.map(t=>{let s=t.timeSlots.find(t=>t.hour===e),a=v(t.date,e);return(0,r.jsx)(el,{timeSlot:s,onClick:s=>((e,t,s)=>{let r=v(e,t);if(s.shiftKey&&u){let s=new Set(m),{date:r,hour:a}=u;if(e===r){let[r,l]=[t,a].sort((e,t)=>e-t);for(let t=r;t<=l;t++)s.add(v(e,t))}x(s)}else if(s.metaKey||s.ctrlKey){s.preventDefault();let e=new Set(m);e.has(r)?e.delete(r):e.add(r),x(e),n(null),d(null)}else n({date:e,hour:t}),x(new Set);g({date:e,hour:t})})(t.date,e,s),onPlanClick:s=>((e,t,s)=>{let r=v(e,t);if(s.shiftKey&&u){let s=new Set(m),{date:r,hour:a}=u;if(e===r){let[r,l]=[t,a].sort((e,t)=>e-t);for(let t=r;t<=l;t++)s.add(v(e,t))}x(s)}else if(s.metaKey||s.ctrlKey){s.preventDefault();let e=new Set(m);e.has(r)?e.delete(r):e.add(r),x(e),n(null),d(null)}else d({date:e,hour:t}),x(new Set);g({date:e,hour:t})})(t.date,e,s),onClearCell:()=>k(t.date,e),isToday:(0,l.cK)(t.dateObj),showPlanSection:!0,isSelected:m.has(a)},a)})]},e))]})}),m.size>1&&(0,r.jsx)("div",{className:"fixed bottom-4 left-4 right-4 md:bottom-20 md:left-1/2 md:right-auto md:-translate-x-1/2 md:w-auto bg-white shadow-lg rounded-lg p-3 z-50 border border-gray-200",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center gap-2 md:gap-4",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-800",children:[m.size," slots selected"]}),(0,r.jsxs)("div",{className:"flex gap-2 w-full md:w-auto",children:[(0,r.jsx)(i,{onClick:()=>y(!0),className:"flex-1 md:flex-none text-sm",size:"sm",children:"Plan"}),(0,r.jsx)(i,{onClick:()=>b(!0),variant:"secondary",className:"flex-1 md:flex-none text-sm",size:"sm",children:"Track"}),(0,r.jsx)(i,{variant:"ghost",onClick:()=>x(new Set),className:"flex-1 md:flex-none text-sm",size:"sm",children:"Clear"})]})]})}),s&&(0,r.jsx)(er,{date:s.date,hour:s.hour,onClose:()=>{n(null)}}),c&&(0,r.jsx)(es,{date:c.date,hour:c.hour,onClose:()=>{d(null)}}),h&&(0,r.jsx)(Z,{selectedSlots:Array.from(m).map(e=>{let[t,s]=e.split("_");return{date:t,hour:parseInt(s,10)}}),onClose:()=>{y(!1),x(new Set)}}),p&&(0,r.jsx)(et,{selectedSlots:Array.from(m).map(e=>{let[t,s]=e.split("_");return{date:t,hour:parseInt(s,10)}}),onClose:()=>{b(!1),x(new Set)}})]})}var ei=s(6132);function eo(e){let{message:t,onRetry:s}=e;return(0,r.jsx)("div",{className:"card max-w-md mx-auto text-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)(ei.A,{className:"h-12 w-12 text-red-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Something went wrong"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:t})]}),s&&(0,r.jsx)("button",{onClick:s,className:"btn-primary",children:"Try Again"})]})})}var ec=s(9540);function ed(){let{activeTab:e}=D(),{state:t}=(0,o.A)(),[s,l]=(0,a.useState)(!1),[i,c]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{let e=()=>{c(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,a.useEffect)(()=>{l(!1)},[e]),t.isLoading)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(n,{})}):t.error?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(eo,{message:t.error})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(v,{}),i?(0,r.jsxs)("div",{className:"h-[calc(100vh-73px)] flex flex-col",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-4 bg-white border-b border-gray-200 md:hidden",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"stats"===e?"Statistics":"Categories"}),(0,r.jsx)("button",{onClick:()=>l(!s),className:"p-2 rounded-lg hover:bg-gray-100 transition-colors","aria-label":"Toggle menu",children:s?(0,r.jsx)(_.A,{className:"h-6 w-6 text-gray-600"}):(0,r.jsx)(ec.A,{className:"h-6 w-6 text-gray-600"})})]}),s&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>l(!1)}),(0,r.jsx)("div",{className:"fixed top-[73px] left-0 w-80 h-[calc(100vh-73px)] bg-white z-50 transform transition-transform duration-300 ease-in-out",children:(0,r.jsx)(X,{})})]}),(0,r.jsx)("main",{className:"flex-1 overflow-auto",children:(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)(en,{})})})]}):(0,r.jsx)("div",{className:"flex h-[calc(100vh-73px)]",children:"stats"===e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(B,{defaultWidth:450,minWidth:350,maxWidth:800,storageKey:"stats-panel-width",children:(0,r.jsx)("div",{className:"h-full bg-white",children:(0,r.jsx)(X,{})})}),(0,r.jsx)("main",{className:"flex-1 p-6 overflow-auto",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsx)(en,{})})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(B,{defaultWidth:320,minWidth:250,maxWidth:500,storageKey:"sidebar-width",children:(0,r.jsx)(X,{})}),(0,r.jsx)("main",{className:"flex-1 p-6 overflow-auto",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsx)(en,{})})})]})})]})}function em(){return(0,r.jsx)(A,{children:(0,r.jsx)(ed,{})})}},2855:(e,t,s)=>{Promise.resolve().then(s.bind(s,2106))}},e=>{e.O(0,[647,430,565,86,441,255,358],()=>e(e.s=2855)),_N_E=e.O()}]);