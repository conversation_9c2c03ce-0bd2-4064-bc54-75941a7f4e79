"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[86],{3086:(t,e,a)=>{a.d(e,{TimeTrackingProvider:()=>T,A:()=>g});var r=a(5155),i=a(8566),n=a(5194);class s{static fromFormData(t){return new s({name:t.name,color:t.color})}static createDefaults(){return[{name:"Exercise",color:i.Gv[2]},{name:"Family/Social",color:i.Gv[3]},{name:"<PERSON>",color:i.Gv[4]},{name:"Reading",color:i.Gv[6]},{name:"Study/Job",color:i.Gv[1]},{name:"Study/NonJob",color:i.Gv[2]},{name:"Work/Coding",color:i.Gv[0]},{name:"Work/NonCoding",color:i.Gv[5]}].map(t=>new s(t))}update(t){return new s({...this,...t,updatedAt:new Date})}toJSON(){return{id:this.id,name:this.name,color:this.color,createdAt:this.createdAt,updatedAt:this.updatedAt}}validate(){if(!this.name||0===this.name.length)throw Error("Category name is required");if(this.name.length>50)throw Error("Category name must be 50 characters or less");if(!this.color||!this.isValidColor(this.color))throw Error("Valid color is required")}isValidColor(t){return/^#[0-9A-F]{6}$/i.test(t)}getContrastColor(){let t=parseInt(this.color.slice(1,3),16),e=parseInt(this.color.slice(3,5),16);return(.299*t+.587*e+.114*parseInt(this.color.slice(5,7),16))/255>.5?"#000000":"#FFFFFF"}equals(t){return this.id===t.id}constructor(t){this.id=t.id||(0,n.$C)(),this.name=t.name.trim(),this.color=t.color,this.createdAt=t.createdAt||new Date,this.updatedAt=t.updatedAt||new Date,this.validate()}}class o{static fromFormData(t,e,a){return new o({date:t,hour:e,categoryId:a.categoryId,isImportant:a.isImportant,isUrgent:a.isUrgent,description:a.description})}update(t){return new o({...this,...t,updatedAt:new Date})}getQuadrant(){return this.isImportant&&this.isUrgent?i.eW.IMPORTANT_URGENT:this.isImportant&&!this.isUrgent?i.eW.IMPORTANT_NOT_URGENT:!this.isImportant&&this.isUrgent?i.eW.NOT_IMPORTANT_URGENT:i.eW.NOT_IMPORTANT_NOT_URGENT}getTimeString(){let t=this.hour>12?this.hour-12:this.hour,e=this.hour>=12?"PM":"AM";return"".concat(t,":00 ").concat(e)}getFormattedDate(){return new Date(this.date).toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"})}toJSON(){return{id:this.id,date:this.date,hour:this.hour,categoryId:this.categoryId,isImportant:this.isImportant,isUrgent:this.isUrgent,description:this.description,createdAt:this.createdAt,updatedAt:this.updatedAt}}validate(){if(!(0,n.vd)(this.date))throw Error("Invalid date format. Expected YYYY-MM-DD");if(!Number.isInteger(this.hour)||this.hour<i.Oc.START||this.hour>i.Oc.END)throw Error("Hour must be between ".concat(i.Oc.START," and ").concat(i.Oc.END));if(!this.categoryId||0===this.categoryId.trim().length)throw Error("Category ID is required");if(this.description&&this.description.length>200)throw Error("Description must be 200 characters or less")}equals(t){return this.id===t.id}conflictsWith(t){return this.date===t.date&&this.hour===t.hour&&this.id!==t.id}constructor(t){var e;this.id=t.id||(0,n.$C)(),this.date=t.date,this.hour=t.hour,this.categoryId=t.categoryId,this.isImportant=t.isImportant,this.isUrgent=t.isUrgent,this.description=(null==(e=t.description)?void 0:e.trim())||void 0,this.createdAt=t.createdAt||new Date,this.updatedAt=t.updatedAt||new Date,this.validate()}}class d{static fromFormData(t,e,a){return new d({date:t,hour:e,categoryId:a.categoryId,isImportant:a.isImportant,isUrgent:a.isUrgent,description:a.description})}update(t){return new d({...this,...t,updatedAt:new Date})}getQuadrant(){return this.isImportant&&this.isUrgent?i.eW.IMPORTANT_URGENT:this.isImportant&&!this.isUrgent?i.eW.IMPORTANT_NOT_URGENT:!this.isImportant&&this.isUrgent?i.eW.NOT_IMPORTANT_URGENT:i.eW.NOT_IMPORTANT_NOT_URGENT}getTimeString(){let t=this.hour>12?this.hour-12:this.hour,e=this.hour>=12?"PM":"AM";return"".concat(t,":00 ").concat(e)}getFormattedDate(){return new Date(this.date).toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"})}toJSON(){return{id:this.id,date:this.date,hour:this.hour,categoryId:this.categoryId,isImportant:this.isImportant,isUrgent:this.isUrgent,description:this.description,createdAt:this.createdAt,updatedAt:this.updatedAt}}validate(){if(!(0,n.vd)(this.date))throw Error("Invalid date format. Expected YYYY-MM-DD");if(!Number.isInteger(this.hour)||this.hour<i.Oc.START||this.hour>i.Oc.END)throw Error("Hour must be between ".concat(i.Oc.START," and ").concat(i.Oc.END));if(!this.categoryId||0===this.categoryId.trim().length)throw Error("Category ID is required");if(this.description&&this.description.length>200)throw Error("Description must be 200 characters or less")}equals(t){return this.id===t.id}conflictsWith(t){return this.date===t.date&&this.hour===t.hour&&this.id!==t.id}constructor(t){var e;this.id=t.id||(0,n.$C)(),this.date=t.date,this.hour=t.hour,this.categoryId=t.categoryId,this.isImportant=t.isImportant,this.isUrgent=t.isUrgent,this.description=(null==(e=t.description)?void 0:e.trim())||void 0,this.createdAt=t.createdAt||new Date,this.updatedAt=t.updatedAt||new Date,this.validate()}}class l{async initialize(){this.isLoaded||(this.data=await this.storageService.loadData(),this.isLoaded=!0)}async saveData(){this.data&&await this.storageService.saveData(this.data)}ensureLoaded(){if(!this.isLoaded||!this.data)throw Error("Repository not initialized. Call initialize() first.")}async getCategories(){return this.ensureLoaded(),this.data.categories.map(t=>new s(t))}async getCategoryById(t){this.ensureLoaded();let e=this.data.categories.find(e=>e.id===t);return e?new s(e):null}async createCategory(t){if(this.ensureLoaded(),this.data.categories.find(e=>e.name.toLowerCase()===t.name.toLowerCase()))throw Error("Category with this name already exists");let e=s.fromFormData(t);return this.data.categories.push(e.toJSON()),await this.saveData(),e}async updateCategory(t,e){this.ensureLoaded();let a=this.data.categories.findIndex(e=>e.id===t);if(-1===a)throw Error("Category not found");if(e.name&&this.data.categories.find(a=>a.id!==t&&a.name.toLowerCase()===e.name.toLowerCase()))throw Error("Category with this name already exists");let r=new s(this.data.categories[a]).update(e);return this.data.categories[a]=r.toJSON(),await this.saveData(),r}async deleteCategory(t){if(this.ensureLoaded(),this.data.timeEntries.some(e=>e.categoryId===t))throw Error("Cannot delete category that is in use");let e=this.data.categories.findIndex(e=>e.id===t);if(-1===e)throw Error("Category not found");this.data.categories.splice(e,1),await this.saveData()}async getTimeEntries(){return this.ensureLoaded(),this.data.timeEntries.map(t=>new d(t))}async getPlannedEntries(){return this.ensureLoaded(),this.data.plannedEntries.map(t=>new o(t))}async getTimeEntriesForDateRange(t,e){this.ensureLoaded();let a=(0,n.bU)(t),r=(0,n.bU)(e);return this.data.timeEntries.filter(t=>t.date>=a&&t.date<=r).map(t=>new d(t))}async getTimeEntry(t,e){this.ensureLoaded();let a=this.data.timeEntries.find(a=>a.date===t&&a.hour===e);return a?new d(a):null}async upsertTimeEntry(t,e,a){if(this.ensureLoaded(),!this.data.categories.some(t=>t.id===a.categoryId))throw Error("Selected category does not exist");let r=this.data.timeEntries.findIndex(a=>a.date===t&&a.hour===e),i=d.fromFormData(t,e,a);return r>=0?this.data.timeEntries[r]=i.toJSON():this.data.timeEntries.push(i.toJSON()),await this.saveData(),i}async deleteTimeEntry(t,e){this.ensureLoaded();let a=this.data.timeEntries.findIndex(a=>a.date===t&&a.hour===e);if(-1===a)throw Error("Time entry not found");this.data.timeEntries.splice(a,1),await this.saveData()}async createPlannedEntry(t,e,a){this.ensureLoaded();let r=o.fromFormData(t,e,a),i=this.data.plannedEntries.findIndex(a=>a.date===t&&a.hour===e);return i>=0?this.data.plannedEntries[i]=r.toJSON():this.data.plannedEntries.push(r.toJSON()),await this.saveData(),r}async updatePlannedEntry(t,e){this.ensureLoaded();let a=this.data.plannedEntries.findIndex(e=>e.id===t);if(-1===a)throw Error("Planned entry not found");let r=new o(this.data.plannedEntries[a]).update(e);return this.data.plannedEntries[a]=r.toJSON(),await this.saveData(),r}async deletePlannedEntry(t){this.ensureLoaded();let e=this.data.plannedEntries.findIndex(e=>e.id===t);if(-1===e)throw Error("Planned entry not found");this.data.plannedEntries.splice(e,1),await this.saveData()}async batchCreatePlannedEntries(t,e){this.ensureLoaded();let a=t.map(t=>o.fromFormData(t.date,t.hour,e));return a.forEach(t=>{let e=this.data.plannedEntries.findIndex(e=>e.date===t.date&&e.hour===t.hour);e>=0?this.data.plannedEntries[e]=t.toJSON():this.data.plannedEntries.push(t.toJSON())}),await this.saveData(),a}async batchUpsertTimeEntries(t,e){if(this.ensureLoaded(),!this.data.categories.some(t=>t.id===e.categoryId))throw Error("Selected category does not exist");let a=t.map(t=>d.fromFormData(t.date,t.hour,e));return a.forEach(t=>{let e=this.data.timeEntries.findIndex(e=>e.date===t.date&&e.hour===t.hour);e>=0?this.data.timeEntries[e]=t.toJSON():this.data.timeEntries.push(t.toJSON())}),await this.saveData(),a}async clearAllCategories(){this.ensureLoaded(),this.data.categories=[],await this.saveData()}async loadDefaultCategories(){this.ensureLoaded();let t=s.createDefaults();return this.data.categories=t.map(t=>t.toJSON()),await this.saveData(),t}async clearAllData(){await this.storageService.clear(),this.data=await this.storageService.loadData()}async getAppData(){return this.ensureLoaded(),{...this.data}}constructor(t){this.storageService=t,this.data=null,this.isLoaded=!1}}class c{async loadData(){try{let t=localStorage.getItem(this.STORAGE_KEY);if(!t)return this.createDefaultData();let e=JSON.parse(t);if(e.version!==this.CURRENT_VERSION)return this.migrateData(e);return this.deserializeData(e)}catch(t){return console.error("Failed to load data from localStorage:",t),this.createDefaultData()}}async saveData(t){try{let e=this.serializeData(t);localStorage.setItem(this.STORAGE_KEY,JSON.stringify(e))}catch(t){throw console.error("Failed to save data to localStorage:",t),Error("Failed to save data")}}async clear(){try{localStorage.removeItem(this.STORAGE_KEY)}catch(t){throw console.error("Failed to clear localStorage:",t),Error("Failed to clear data")}}createDefaultData(){return{categories:s.createDefaults().map(t=>t.toJSON()),timeEntries:[],plannedEntries:[],settings:{defaultView:"weekly",theme:"light",weekStartsOn:1,defaultMode:i.$0.TRACKING},version:this.CURRENT_VERSION}}serializeData(t){return{...t,categories:t.categories.map(t=>({...t,createdAt:t.createdAt.toISOString(),updatedAt:t.updatedAt.toISOString()})),timeEntries:t.timeEntries.map(t=>({...t,createdAt:t.createdAt.toISOString(),updatedAt:t.updatedAt.toISOString()})),plannedEntries:t.plannedEntries.map(t=>({...t,createdAt:t.createdAt.toISOString(),updatedAt:t.updatedAt.toISOString()}))}}deserializeData(t){return{...t,categories:t.categories.map(t=>({...t,createdAt:new Date(t.createdAt),updatedAt:new Date(t.updatedAt)})),timeEntries:t.timeEntries.map(t=>({...t,createdAt:new Date(t.createdAt),updatedAt:new Date(t.updatedAt)})),plannedEntries:(t.plannedEntries||[]).map(t=>({...t,createdAt:new Date(t.createdAt),updatedAt:new Date(t.updatedAt)}))}}migrateData(t){return console.warn("Migrating data from version ".concat(t.version," to ").concat(this.CURRENT_VERSION)),this.createDefaultData()}static isAvailable(){try{let t="__localStorage_test__";return localStorage.setItem(t,t),localStorage.removeItem(t),!0}catch(t){return!1}}getStorageInfo(){if(!c.isAvailable())return{used:0,available:0,percentage:0};let t=0;for(let e in localStorage)localStorage.hasOwnProperty(e)&&(t+=localStorage[e].length+e.length);let e=t/5242880*100;return{used:t,available:5242880,percentage:e}}constructor(){this.STORAGE_KEY="time_track_app_data",this.CURRENT_VERSION="1.0.0"}}var E=a(2115);let h={categories:[],timeEntries:[],plannedEntries:[],isLoading:!0,error:null,currentWeek:(0,n.lV)(),chartFilters:{selectedCategories:[],selectedQuadrants:[]}};function u(t,e){switch(e.type){case"SET_LOADING":return{...t,isLoading:e.payload};case"SET_ERROR":return{...t,error:e.payload,isLoading:!1};case"SET_CATEGORIES":return{...t,categories:e.payload};case"SET_TIME_ENTRIES":return{...t,timeEntries:e.payload};case"ADD_CATEGORY":return{...t,categories:[...t.categories,e.payload]};case"UPDATE_CATEGORY":return{...t,categories:t.categories.map(t=>t.id===e.payload.id?e.payload:t)};case"DELETE_CATEGORY":return{...t,categories:t.categories.filter(t=>t.id!==e.payload)};case"UPSERT_TIME_ENTRY":let a=t.timeEntries.findIndex(t=>t.date===e.payload.date&&t.hour===e.payload.hour);if(a>=0)return{...t,timeEntries:t.timeEntries.map((t,r)=>r===a?e.payload:t)};return{...t,timeEntries:[...t.timeEntries,e.payload]};case"BATCH_UPSERT_TIME_ENTRIES":let r=[...t.timeEntries];return e.payload.forEach(t=>{let e=r.findIndex(e=>e.date===t.date&&e.hour===t.hour);e>=0?r[e]=t:r.push(t)}),{...t,timeEntries:r};case"DELETE_TIME_ENTRY":return{...t,timeEntries:t.timeEntries.filter(t=>t.date!==e.payload.date||t.hour!==e.payload.hour)};case"SET_PLANNED_ENTRIES":return{...t,plannedEntries:e.payload};case"UPSERT_PLANNED_ENTRY":let i=t.plannedEntries.findIndex(t=>t.id===e.payload.id);if(!(i>=0))return{...t,plannedEntries:[...t.plannedEntries,e.payload]};{let a=[...t.plannedEntries];return a[i]=e.payload,{...t,plannedEntries:a}}case"DELETE_PLANNED_ENTRY":return{...t,plannedEntries:t.plannedEntries.filter(t=>t.id!==e.payload)};case"BATCH_ADD_PLANNED_ENTRIES":let s=[...t.plannedEntries];return e.payload.forEach(t=>{let e=s.findIndex(e=>e.date===t.date&&e.hour===t.hour);e>=0?s[e]=t:s.push(t)}),{...t,plannedEntries:s};case"SET_CURRENT_WEEK":return{...t,currentWeek:e.payload};case"TOGGLE_CATEGORY_FILTER":let o=e.payload,d=t.chartFilters.selectedCategories.includes(o);return{...t,chartFilters:{...t.chartFilters,selectedCategories:d?t.chartFilters.selectedCategories.filter(t=>t!==o):[...t.chartFilters.selectedCategories,o]}};case"SET_DATE_RANGE_FILTER":return{...t,chartFilters:{...t.chartFilters,dateRange:e.payload}};case"CLEAR_DATE_RANGE_FILTER":return{...t,chartFilters:{...t.chartFilters,dateRange:void 0}};case"TOGGLE_QUADRANT_FILTER":let l=e.payload,c=t.chartFilters.selectedQuadrants.includes(l);return{...t,chartFilters:{...t.chartFilters,selectedQuadrants:c?t.chartFilters.selectedQuadrants.filter(t=>t!==l):[...t.chartFilters.selectedQuadrants,l]}};case"CLEAR_ALL_FILTERS":return{...t,chartFilters:{selectedCategories:[],selectedQuadrants:[]}};case"SELECT_ONLY_CATEGORY":return{...t,chartFilters:{...t.chartFilters,selectedCategories:[e.payload]}};case"HIDE_ALL_CATEGORIES":return{...t,chartFilters:{...t.chartFilters,selectedCategories:t.categories.map(t=>t.id)}};case"CLEAR_WEEK_DATA":let E=e.payload,h=(0,n._2)(E).map(t=>(0,n.bU)(t));return{...t,timeEntries:t.timeEntries.filter(t=>!h.includes(t.date)),plannedEntries:t.plannedEntries.filter(t=>!h.includes(t.date))};case"CLEAR_CELL_DATA":return{...t,timeEntries:t.timeEntries.filter(t=>t.date!==e.payload.date||t.hour!==e.payload.hour),plannedEntries:t.plannedEntries.filter(t=>t.date!==e.payload.date||t.hour!==e.payload.hour)};default:return t}}let p=(0,E.createContext)(null),y=new l(new c);function T(t){let{children:e}=t,[a,i]=(0,E.useReducer)(u,h);return(0,E.useEffect)(()=>{(async()=>{try{i({type:"SET_LOADING",payload:!0}),await y.initialize();let[t,e,a]=await Promise.all([y.getCategories(),y.getTimeEntries(),y.getPlannedEntries()]),r=t.map(t=>t.toJSON()),n=e.map(t=>t.toJSON()),s=a.map(t=>t.toJSON());i({type:"SET_CATEGORIES",payload:r}),i({type:"SET_TIME_ENTRIES",payload:n}),i({type:"SET_PLANNED_ENTRIES",payload:s}),i({type:"SET_ERROR",payload:null})}catch(t){console.error("Failed to initialize data:",t),i({type:"SET_ERROR",payload:"Failed to load data"})}finally{i({type:"SET_LOADING",payload:!1})}})()},[]),(0,r.jsx)(p.Provider,{value:{state:a,actions:{createCategory:async t=>{try{let e=(await y.createCategory(t)).toJSON();i({type:"ADD_CATEGORY",payload:e})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to create category"}),t}},updateCategory:async(t,e)=>{try{let a=(await y.updateCategory(t,e)).toJSON();i({type:"UPDATE_CATEGORY",payload:a})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to update category"}),t}},deleteCategory:async t=>{try{await y.deleteCategory(t),i({type:"DELETE_CATEGORY",payload:t})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to delete category"}),t}},upsertTimeEntry:async(t,e,a)=>{try{let r=(await y.upsertTimeEntry(t,e,a)).toJSON();i({type:"UPSERT_TIME_ENTRY",payload:r})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to save time entry"}),t}},batchUpsertTimeEntries:async(t,e)=>{try{let a=(await y.batchUpsertTimeEntries(t,e)).map(t=>t.toJSON());i({type:"BATCH_UPSERT_TIME_ENTRIES",payload:a})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to batch create time entries"}),t}},deleteTimeEntry:async(t,e)=>{try{await y.deleteTimeEntry(t,e),i({type:"DELETE_TIME_ENTRY",payload:{date:t,hour:e}})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to delete time entry"}),t}},createPlannedEntry:async(t,e,a)=>{try{let r=(await y.createPlannedEntry(t,e,a)).toJSON();i({type:"UPSERT_PLANNED_ENTRY",payload:r})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to create planned entry"}),t}},updatePlannedEntry:async(t,e)=>{try{let a=(await y.updatePlannedEntry(t,e)).toJSON();i({type:"UPSERT_PLANNED_ENTRY",payload:a})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to update planned entry"}),t}},deletePlannedEntry:async t=>{try{await y.deletePlannedEntry(t),i({type:"DELETE_PLANNED_ENTRY",payload:t})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to delete planned entry"}),t}},batchCreatePlannedEntries:async(t,e)=>{try{let a=(await y.batchCreatePlannedEntries(t,e)).map(t=>t.toJSON());i({type:"BATCH_ADD_PLANNED_ENTRIES",payload:a})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to batch create planned entries"}),t}},setCurrentWeek:t=>{i({type:"SET_CURRENT_WEEK",payload:t})},clearAllData:async()=>{try{await y.clearAllData(),i({type:"SET_CATEGORIES",payload:[]}),i({type:"SET_TIME_ENTRIES",payload:[]});let t=(await y.getCategories()).map(t=>t.toJSON());i({type:"SET_CATEGORIES",payload:t})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to clear data"}),t}},refreshData:async()=>{try{i({type:"SET_LOADING",payload:!0});let[t,e]=await Promise.all([y.getCategories(),y.getTimeEntries()]),a=t.map(t=>t.toJSON()),r=e.map(t=>t.toJSON());i({type:"SET_CATEGORIES",payload:a}),i({type:"SET_TIME_ENTRIES",payload:r}),i({type:"SET_ERROR",payload:null})}catch(t){i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to refresh data"})}finally{i({type:"SET_LOADING",payload:!1})}},toggleCategoryFilter:t=>{i({type:"TOGGLE_CATEGORY_FILTER",payload:t})},setDateRangeFilter:(t,e)=>{i({type:"SET_DATE_RANGE_FILTER",payload:{startDate:t,endDate:e}})},clearDateRangeFilter:()=>{i({type:"CLEAR_DATE_RANGE_FILTER"})},toggleQuadrantFilter:t=>{i({type:"TOGGLE_QUADRANT_FILTER",payload:t})},clearAllFilters:()=>{i({type:"CLEAR_ALL_FILTERS"})},selectOnlyCategory:t=>{i({type:"SELECT_ONLY_CATEGORY",payload:t})},hideAllCategories:()=>{i({type:"HIDE_ALL_CATEGORIES"})},clearWeekData:async t=>{try{let e=(0,n._2)(t).map(t=>(0,n.bU)(t));await Promise.all(e.flatMap(t=>Array.from({length:14},(t,e)=>e+9).map(e=>y.deleteTimeEntry(t,e).catch(()=>{}))));let r=a.plannedEntries.filter(t=>e.includes(t.date));await Promise.all(r.map(t=>y.deletePlannedEntry(t.id).catch(()=>{}))),i({type:"CLEAR_WEEK_DATA",payload:t})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to clear week data"}),t}},clearCellData:async(t,e)=>{try{await y.deleteTimeEntry(t,e).catch(()=>{});let r=a.plannedEntries.find(a=>a.date===t&&a.hour===e);r&&await y.deletePlannedEntry(r.id).catch(()=>{}),i({type:"CLEAR_CELL_DATA",payload:{date:t,hour:e}})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to clear cell data"}),t}},loadDefaultCategories:async()=>{try{await y.clearAllCategories();let t=(await y.loadDefaultCategories()).map(t=>t.toJSON());i({type:"SET_CATEGORIES",payload:t})}catch(t){throw i({type:"SET_ERROR",payload:t instanceof Error?t.message:"Failed to load default categories"}),t}}}},children:e})}function g(){let t=(0,E.useContext)(p);if(!t)throw Error("useTimeTracking must be used within a TimeTrackingProvider");return t}},5194:(t,e,a)=>{a.d(e,{$4:()=>I,$C:()=>p,F_:()=>D,KY:()=>S,Y6:()=>R,_2:()=>m,bU:()=>T,cK:()=>N,cn:()=>u,k2:()=>_,lV:()=>h,oE:()=>A,uA:()=>f,vd:()=>y});var r=a(8566),i=a(2821),n=a(2192),s=a(1321),o=a(7205),d=a(851),l=a(3796),c=a(9791);let E="America/Los_Angeles";function h(){return(0,c.L_)(new Date,E)}function u(){for(var t=arguments.length,e=Array(t),a=0;a<t;a++)e[a]=arguments[a];return(0,i.$)(e)}function p(){return"".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9))}function y(t){if(!/^\d{4}-\d{2}-\d{2}$/.test(t))return!1;let e=new Date(t);return e instanceof Date&&!isNaN(e.getTime())}function T(t){return(0,c.GP)(t,"yyyy-MM-dd",{timeZone:E})}function g(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return{start:(0,n.k)(t,{weekStartsOn:e}),end:(0,s.$)(t,{weekStartsOn:e})}}function m(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,{start:a,end:r}=g(t,e);return(0,o.k)({start:a,end:r})}function R(t){return(0,d.J)(t,1)}function A(t){return(0,l.k)(t,1)}function _(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,{start:a,end:r}=g(t,e),i={timeZone:E};return(0,c.GP)(a,"M",i)===(0,c.GP)(r,"M",i)?"".concat((0,c.GP)(a,"MMM d",i)," - ").concat((0,c.GP)(r,"d, yyyy",i)):"".concat((0,c.GP)(a,"MMM d",i)," - ").concat((0,c.GP)(r,"MMM d, yyyy",i))}function N(t){let e=h();return T(t)===T(e)}function S(t){return 0===t?"12:00 AM":t<12?"".concat(t,":00 AM"):12===t?"12:00 PM":"".concat(t-12,":00 PM")}function f(t){return(0,c.GP)(t,"EEE",{timeZone:E})}function I(t,e){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return 0===e?0:Number((t/e*100).toFixed(a))}function D(t,e,a){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];return t.map(t=>{let n=T(t),s=[];for(let t=9;t<=22;t++){let o,d=e.find(e=>e.date===n&&e.hour===t),l=d?a.find(t=>t.id===d.categoryId):void 0,c=i.find(e=>e.date===n&&e.hour===t),E=c?a.find(t=>t.id===c.categoryId):void 0;o=c&&d?c.categoryId===d.categoryId?r.rI.COMPLETED:r.rI.UNPLANNED:c&&!d?r.rI.MISSED:!c&&d?r.rI.UNPLANNED:r.rI.PLANNED,s.push({date:n,hour:t,actualEntry:d,actualCategory:l,plannedEntry:c,plannedCategory:E,executionStatus:o,entry:d,category:l})}return{dateObj:t,date:n,dayName:(0,c.GP)(t,"EEEE",{timeZone:E}),timeSlots:s}})}},8566:(t,e,a)=>{a.d(e,{$0:()=>n,Gv:()=>s,Oc:()=>o,eW:()=>r,r1:()=>d,rI:()=>i});var r=function(t){return t.IMPORTANT_URGENT="Q1",t.IMPORTANT_NOT_URGENT="Q2",t.NOT_IMPORTANT_URGENT="Q3",t.NOT_IMPORTANT_NOT_URGENT="Q4",t}({}),i=function(t){return t.PLANNED="planned",t.EXECUTED="executed",t.COMPLETED="completed",t.MISSED="missed",t.UNPLANNED="unplanned",t}({}),n=function(t){return t.PLANNING="planning",t.TRACKING="tracking",t.COMPARISON="comparison",t}({});let s=["#3B82F6","#EF4444","#10B981","#F59E0B","#8B5CF6","#F97316","#06B6D4","#84CC16","#EC4899","#6B7280","#14B8A6","#F43F5E"],o={START:9,END:22,TOTAL:14},d=7*o.TOTAL}}]);