"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[565],{368:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},532:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},534:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},814:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},877:(t,e,r)=>{r.d(e,{Fq:()=>f});var n,i=r(2115),a=r(1607);let o="label";function l(t,e){"function"==typeof t?t(e):t&&(t.current=e)}function s(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:o,n=[];t.datasets=e.map(e=>{let i=t.datasets.find(t=>t[r]===e[r]);return!i||!e.data||n.includes(i)?{...e}:(n.push(i),Object.assign(i,e),i)})}let c=(0,i.forwardRef)(function(t,e){let{height:r=150,width:n=300,redraw:c=!1,datasetIdKey:f,type:u,data:h,options:d,plugins:p=[],fallbackContent:g,updateMode:b,...y}=t,m=(0,i.useRef)(null),x=(0,i.useRef)(null),k=()=>{m.current&&(x.current=new a.t1(m.current,{type:u,data:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o,r={labels:[],datasets:[]};return r.labels=t.labels,s(r,t.datasets,e),r}(h,f),options:d&&{...d},plugins:p}),l(e,x.current))},v=()=>{l(e,null),x.current&&(x.current.destroy(),x.current=null)};return(0,i.useEffect)(()=>{!c&&x.current&&d&&function(t,e){let r=t.options;r&&e&&Object.assign(r,e)}(x.current,d)},[c,d]),(0,i.useEffect)(()=>{!c&&x.current&&(x.current.config.data.labels=h.labels)},[c,h.labels]),(0,i.useEffect)(()=>{!c&&x.current&&h.datasets&&s(x.current.config.data,h.datasets,f)},[c,h.datasets]),(0,i.useEffect)(()=>{x.current&&(c?(v(),setTimeout(k)):x.current.update(b))},[c,d,h.labels,h.datasets,b]),(0,i.useEffect)(()=>{x.current&&(v(),setTimeout(k))},[u]),(0,i.useEffect)(()=>(k(),()=>v()),[]),i.createElement("canvas",{ref:m,role:"img",height:r,width:n,...y},g)}),f=(n=a.P$,a.t1.register(n),(0,i.forwardRef)((t,e)=>i.createElement(c,{...t,ref:e,type:"pie"})))},1190:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]])},1360:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},1524:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},1847:(t,e,r)=>{r.d(e,{A:()=>s});var n=r(2115);let i=t=>{let e=t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,r)=>r?r.toUpperCase():e.toLowerCase());return e.charAt(0).toUpperCase()+e.slice(1)},a=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return e.filter((t,e,r)=>!!t&&""!==t.trim()&&r.indexOf(t)===e).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,n.forwardRef)((t,e)=>{let{color:r="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:s,className:c="",children:f,iconNode:u,...h}=t;return(0,n.createElement)("svg",{ref:e,...o,width:i,height:i,stroke:r,strokeWidth:s?24*Number(l)/Number(i):l,className:a("lucide",c),...!f&&!(t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0})(h)&&{"aria-hidden":"true"},...h},[...u.map(t=>{let[e,r]=t;return(0,n.createElement)(e,r)}),...Array.isArray(f)?f:[f]])}),s=(t,e)=>{let r=(0,n.forwardRef)((r,o)=>{let{className:s,...c}=r;return(0,n.createElement)(l,{ref:o,iconNode:e,className:a("lucide-".concat(i(t).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(t),s),...c})});return r.displayName=i(t),r}},2698:(t,e,r)=>{let n;function i(t){return t+.5|0}r.d(e,{$:()=>ec,A:()=>tR,B:()=>tC,C:()=>el,D:()=>tM,E:()=>ek,F:()=>$,G:()=>eQ,H:()=>ts,I:()=>e$,J:()=>eJ,K:()=>eK,L:()=>tY,M:()=>eH,N:()=>tg,O:()=>N,P:()=>tn,Q:()=>H,R:()=>ew,S:()=>tT,T:()=>ti,U:()=>tk,V:()=>ee,W:()=>tP,X:()=>en,Y:()=>es,Z:()=>eh,_:()=>tN,a:()=>eM,a0:()=>ev,a1:()=>tH,a2:()=>t$,a3:()=>t8,a4:()=>V,a5:()=>J,a6:()=>t6,a7:()=>tt,a8:()=>function t(e,r,n,i){return new Proxy({_cacheable:!1,_proxy:e,_context:r,_subProxy:n,_stack:new Set,_descriptors:eA(e,i),setContext:r=>t(e,r,n,i),override:a=>t(e.override(a),r,n,i)},{deleteProperty:(t,r)=>(delete t[r],delete e[r],!0),get:(e,r,n)=>eS(e,r,()=>(function(e,r,n){let{_proxy:i,_context:a,_subProxy:o,_descriptors:l}=e,s=i[r];return tt(s)&&l.isScriptable(r)&&(s=function(t,e,r,n){let{_proxy:i,_context:a,_subProxy:o,_stack:l}=r;if(l.has(t))throw Error("Recursion detected: "+Array.from(l).join("->")+"->"+t);l.add(t);let s=e(a,o||n);return l.delete(t),eP(t,s)&&(s=eC(i._scopes,i,t,s)),s}(r,s,e,n)),W(s)&&s.length&&(s=function(e,r,n,i){let{_proxy:a,_context:o,_subProxy:l,_descriptors:s}=n;if(void 0!==o.index&&i(e))return r[o.index%r.length];if(I(r[0])){let n=r,i=a._scopes.filter(t=>t!==n);for(let c of(r=[],n)){let n=eC(i,a,e,c);r.push(t(n,o,l&&l[e],s))}}return r}(r,s,e,l.isIndexable)),eP(r,s)&&(s=t(s,a,o&&o[r],l)),s})(e,r,n)),getOwnPropertyDescriptor:(t,r)=>t._descriptors.allKeys?Reflect.has(e,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,r),getPrototypeOf:()=>Reflect.getPrototypeOf(e),has:(t,r)=>Reflect.has(e,r),ownKeys:()=>Reflect.ownKeys(e),set:(t,r,n)=>(e[r]=n,delete t[r],!0)})},a9:()=>e_,aA:()=>e2,aB:()=>e5,aC:()=>tD,aD:()=>e4,aE:()=>eo,aF:()=>tw,aG:()=>C,aH:()=>tm,aI:()=>tp,aJ:()=>ty,aK:()=>td,aL:()=>tv,aM:()=>t4,aN:()=>tu,aO:()=>er,aP:()=>tE,aQ:()=>tj,aa:()=>eA,ab:()=>Z,ac:()=>R,ad:()=>tB,ae:()=>eU,af:()=>ei,ag:()=>te,ah:()=>rn,ai:()=>D,aj:()=>tr,ak:()=>tS,al:()=>t_,am:()=>ey,an:()=>eB,ao:()=>e9,ap:()=>e3,aq:()=>e0,ar:()=>e1,as:()=>eG,at:()=>ef,au:()=>eu,av:()=>ea,aw:()=>ed,ax:()=>em,ay:()=>ex,az:()=>e7,b:()=>W,c:()=>tK,d:()=>et,e:()=>tQ,f:()=>K,g:()=>F,h:()=>G,i:()=>I,j:()=>eO,k:()=>E,l:()=>tI,m:()=>Y,n:()=>B,o:()=>t2,p:()=>tA,q:()=>tz,r:()=>tL,s:()=>th,t:()=>tx,u:()=>tF,v:()=>L,w:()=>tq,x:()=>tb,y:()=>eI,z:()=>eV});let a=(t,e,r)=>Math.max(Math.min(t,r),e);function o(t){return a(i(2.55*t),0,255)}function l(t){return a(i(255*t),0,255)}function s(t){return a(i(t/2.55)/100,0,1)}function c(t){return a(i(100*t),0,100)}let f={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},u=[..."0123456789ABCDEF"],h=t=>u[15&t],d=t=>u[(240&t)>>4]+u[15&t],p=t=>(240&t)>>4==(15&t),g=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function b(t,e,r){let n=e*Math.min(r,1-r),i=(e,i=(e+t/30)%12)=>r-n*Math.max(Math.min(i-3,9-i,1),-1);return[i(0),i(8),i(4)]}function y(t,e,r){let n=(n,i=(n+t/60)%6)=>r-r*e*Math.max(Math.min(i,4-i,1),0);return[n(5),n(3),n(1)]}function m(t,e,r){let n,i=b(t,1,.5);for(e+r>1&&(n=1/(e+r),e*=n,r*=n),n=0;n<3;n++)i[n]*=1-e-r,i[n]+=e;return i}function x(t){let e,r,n,i=t.r/255,a=t.g/255,o=t.b/255,l=Math.max(i,a,o),s=Math.min(i,a,o),c=(l+s)/2;l!==s&&(n=l-s,r=c>.5?n/(2-l-s):n/(l+s),e=60*(e=i===l?(a-o)/n+6*(a<o):a===l?(o-i)/n+2:(i-a)/n+4)+.5);return[0|e,r||0,c]}function k(t,e,r,n){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,r,n)).map(l)}function v(t){return(t%360+360)%360}let M={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},w={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},O=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,_=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,A=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function T(t,e,r){if(t){let n=x(t);n[e]=Math.max(0,Math.min(n[e]+n[e]*r,0===e?360:1)),t.r=(n=k(b,n,void 0,void 0))[0],t.g=n[1],t.b=n[2]}}function P(t,e){return t?Object.assign(e||{},t):t}function S(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=l(t[3]))):(e=P(t,{r:0,g:0,b:0,a:1})).a=l(e.a),e}class j{constructor(t){let e;if(t instanceof j)return t;let r=typeof t;"object"===r?e=S(t):"string"===r&&(e=function(t){var e,r=t.length;return"#"===t[0]&&(4===r||5===r?e={r:255&17*f[t[1]],g:255&17*f[t[2]],b:255&17*f[t[3]],a:5===r?17*f[t[4]]:255}:(7===r||9===r)&&(e={r:f[t[1]]<<4|f[t[2]],g:f[t[3]]<<4|f[t[4]],b:f[t[5]]<<4|f[t[6]],a:9===r?f[t[7]]<<4|f[t[8]]:255})),e}(t)||function(t){n||((n=function(){let t,e,r,n,i,a={},o=Object.keys(w),l=Object.keys(M);for(t=0;t<o.length;t++){for(e=0,n=i=o[t];e<l.length;e++)r=l[e],i=i.replace(r,M[r]);r=parseInt(w[n],16),a[i]=[r>>16&255,r>>8&255,255&r]}return a}()).transparent=[0,0,0,0]);let e=n[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||function(t){return"r"===t.charAt(0)?function(t){let e,r,n,i=O.exec(t),l=255;if(i){if(i[7]!==e){let t=+i[7];l=i[8]?o(t):a(255*t,0,255)}return e=+i[1],r=+i[3],n=+i[5],e=255&(i[2]?o(e):a(e,0,255)),{r:e,g:r=255&(i[4]?o(r):a(r,0,255)),b:n=255&(i[6]?o(n):a(n,0,255)),a:l}}}(t):function(t){let e,r=g.exec(t),n=255;if(!r)return;r[5]!==e&&(n=r[6]?o(+r[5]):l(+r[5]));let i=v(+r[2]),a=r[3]/100,s=r[4]/100;return{r:(e="hwb"===r[1]?k(m,i,a,s):"hsv"===r[1]?k(y,i,a,s):k(b,i,a,s))[0],g:e[1],b:e[2],a:n}}(t)}(t)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var t=P(this._rgb);return t&&(t.a=s(t.a)),t}set rgb(t){this._rgb=S(t)}rgbString(){var t;return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${s(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0}hexString(){var t,e;let r,n;return this._valid?(e=p((r=t=this._rgb).r)&&p(r.g)&&p(r.b)&&p(r.a)?h:d,t?"#"+e(t.r)+e(t.g)+e(t.b)+(n=t.a,n<255?e(n):""):void 0):void 0}hslString(){return this._valid?function(t){if(!t)return;let e=x(t),r=e[0],n=c(e[1]),i=c(e[2]);return t.a<255?`hsla(${r}, ${n}%, ${i}%, ${s(t.a)})`:`hsl(${r}, ${n}%, ${i}%)`}(this._rgb):void 0}mix(t,e){if(t){let r,n=this.rgb,i=t.rgb,a=e===r?.5:e,o=2*a-1,l=n.a-i.a,s=((o*l==-1?o:(o+l)/(1+o*l))+1)/2;r=1-s,n.r=255&s*n.r+r*i.r+.5,n.g=255&s*n.g+r*i.g+.5,n.b=255&s*n.b+r*i.b+.5,n.a=a*n.a+(1-a)*i.a,this.rgb=n}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,r){let n=A(s(t.r)),i=A(s(t.g)),a=A(s(t.b));return{r:l(_(n+r*(A(s(e.r))-n))),g:l(_(i+r*(A(s(e.g))-i))),b:l(_(a+r*(A(s(e.b))-a))),a:t.a+r*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new j(this.rgb)}alpha(t){return this._rgb.a=l(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=i(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return T(this._rgb,2,t),this}darken(t){return T(this._rgb,2,-t),this}saturate(t){return T(this._rgb,1,t),this}desaturate(t){return T(this._rgb,1,-t),this}rotate(t){var e,r;return e=this._rgb,(r=x(e))[0]=v(r[0]+t),e.r=(r=k(b,r,void 0,void 0))[0],e.g=r[1],e.b=r[2],this}}function C(){}let R=(()=>{let t=0;return()=>t++})();function E(t){return null==t}function W(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function I(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function F(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function N(t,e){return F(t)?t:e}function L(t,e){return void 0===t?e:t}let Y=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:t/e,B=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function H(t,e,r){if(t&&"function"==typeof t.call)return t.apply(r,e)}function $(t,e,r,n){let i,a,o;if(W(t))if(a=t.length,n)for(i=a-1;i>=0;i--)e.call(r,t[i],i);else for(i=0;i<a;i++)e.call(r,t[i],i);else if(I(t))for(i=0,a=(o=Object.keys(t)).length;i<a;i++)e.call(r,t[o[i]],o[i])}function D(t,e){let r,n,i,a;if(!t||!e||t.length!==e.length)return!1;for(r=0,n=t.length;r<n;++r)if(i=t[r],a=e[r],i.datasetIndex!==a.datasetIndex||i.index!==a.index)return!1;return!0}function z(t){if(W(t))return t.map(z);if(I(t)){let e=Object.create(null),r=Object.keys(t),n=r.length,i=0;for(;i<n;++i)e[r[i]]=z(t[r[i]]);return e}return t}function q(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function X(t,e,r,n){if(!q(t))return;let i=e[t],a=r[t];I(i)&&I(a)?V(i,a,n):e[t]=z(a)}function V(t,e,r){let n,i=W(e)?e:[e],a=i.length;if(!I(t))return t;let o=(r=r||{}).merger||X;for(let e=0;e<a;++e){if(!I(n=i[e]))continue;let a=Object.keys(n);for(let e=0,i=a.length;e<i;++e)o(a[e],t,n,r)}return t}function Z(t,e){return V(t,e,{merger:Q})}function Q(t,e,r){if(!q(t))return;let n=e[t],i=r[t];I(n)&&I(i)?Z(n,i):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=z(i))}let U={"":t=>t,x:t=>t.x,y:t=>t.y};function K(t,e){return(U[e]||(U[e]=function(t){let e=function(t){let e=t.split("."),r=[],n="";for(let t of e)(n+=t).endsWith("\\")?n=n.slice(0,-1)+".":(r.push(n),n="");return r}(t);return t=>{for(let r of e){if(""===r)break;t=t&&t[r]}return t}}(e)))(t)}function J(t){return t.charAt(0).toUpperCase()+t.slice(1)}let G=t=>void 0!==t,tt=t=>"function"==typeof t,te=(t,e)=>{if(t.size!==e.size)return!1;for(let r of t)if(!e.has(r))return!1;return!0};function tr(t){return"mouseup"===t.type||"click"===t.type||"contextmenu"===t.type}let tn=Math.PI,ti=2*tn,ta=ti+tn,to=1/0,tl=tn/180,ts=tn/2,tc=tn/4,tf=2*tn/3,tu=Math.log10,th=Math.sign;function td(t,e,r){return Math.abs(t-e)<r}function tp(t){let e=Math.round(t),r=Math.pow(10,Math.floor(tu(t=td(t,e,t/1e3)?e:t))),n=t/r;return(n<=1?1:n<=2?2:n<=5?5:10)*r}function tg(t){let e,r=[],n=Math.sqrt(t);for(e=1;e<n;e++)t%e==0&&(r.push(e),r.push(t/e));return n===(0|n)&&r.push(n),r.sort((t,e)=>t-e).pop(),r}function tb(t){return"symbol"!=typeof t&&("object"!=typeof t||null===t||!!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function ty(t,e){let r=Math.round(t);return r-e<=t&&r+e>=t}function tm(t,e,r){let n,i,a;for(n=0,i=t.length;n<i;n++)isNaN(a=t[n][r])||(e.min=Math.min(e.min,a),e.max=Math.max(e.max,a))}function tx(t){return tn/180*t}function tk(t){return 180/tn*t}function tv(t){if(!F(t))return;let e=1,r=0;for(;Math.round(t*e)/e!==t;)e*=10,r++;return r}function tM(t,e){let r=e.x-t.x,n=e.y-t.y,i=Math.sqrt(r*r+n*n),a=Math.atan2(n,r);return a<-.5*tn&&(a+=ti),{angle:a,distance:i}}function tw(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function tO(t,e){return(t-e+ta)%ti-tn}function t_(t){return(t%ti+ti)%ti}function tA(t,e,r,n){let i=t_(t),a=t_(e),o=t_(r),l=t_(a-i),s=t_(o-i),c=t_(i-a),f=t_(i-o);return i===a||i===o||n&&a===o||l>s&&c<f}function tT(t,e,r){return Math.max(e,Math.min(r,t))}function tP(t){return tT(t,-32768,32767)}function tS(t,e,r,n=1e-6){return t>=Math.min(e,r)-n&&t<=Math.max(e,r)+n}function tj(t,e,r){let n;r=r||(r=>t[r]<e);let i=t.length-1,a=0;for(;i-a>1;)r(n=a+i>>1)?a=n:i=n;return{lo:a,hi:i}}let tC=(t,e,r,n)=>tj(t,r,n?n=>{let i=t[n][e];return i<r||i===r&&t[n+1][e]===r}:n=>t[n][e]<r),tR=(t,e,r)=>tj(t,r,n=>t[n][e]>=r);function tE(t,e,r){let n=0,i=t.length;for(;n<i&&t[n]<e;)n++;for(;i>n&&t[i-1]>r;)i--;return n>0||i<t.length?t.slice(n,i):t}let tW=["push","pop","shift","splice","unshift"];function tI(t,e){if(t._chartjs)return void t._chartjs.listeners.push(e);Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),tW.forEach(e=>{let r="_onData"+J(e),n=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let i=n.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[r]&&t[r](...e)}),i}})})}function tF(t,e){let r=t._chartjs;if(!r)return;let n=r.listeners,i=n.indexOf(e);-1!==i&&n.splice(i,1),n.length>0||(tW.forEach(e=>{delete t[e]}),delete t._chartjs)}function tN(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let tL="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function tY(t,e){let r=[],n=!1;return function(...i){r=i,n||(n=!0,tL.call(window,()=>{n=!1,t.apply(e,r)}))}}function tB(t,e){let r;return function(...n){return e?(clearTimeout(r),r=setTimeout(t,e,n)):t.apply(this,n),e}}let tH=t=>"start"===t?"left":"end"===t?"right":"center",t$=(t,e,r)=>"start"===t?e:"end"===t?r:(e+r)/2,tD=(t,e,r,n)=>t===(n?"left":"right")?r:"center"===t?(e+r)/2:e;function tz(t,e,r){let n=e.length,i=0,a=n;if(t._sorted){let{iScale:o,vScale:l,_parsed:s}=t,c=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,f=o.axis,{min:u,max:h,minDefined:d,maxDefined:p}=o.getUserBounds();if(d){if(i=Math.min(tC(s,f,u).lo,r?n:tC(e,f,o.getPixelForValue(u)).lo),c){let t=s.slice(0,i+1).reverse().findIndex(t=>!E(t[l.axis]));i-=Math.max(0,t)}i=tT(i,0,n-1)}if(p){let t=Math.max(tC(s,o.axis,h,!0).hi+1,r?0:tC(e,f,o.getPixelForValue(h),!0).hi+1);if(c){let e=s.slice(t-1).findIndex(t=>!E(t[l.axis]));t+=Math.max(0,e)}a=tT(t,i,n)-i}else a=n-i}return{start:i,count:a}}function tq(t){let{xScale:e,yScale:r,_scaleRanges:n}=t,i={xmin:e.min,xmax:e.max,ymin:r.min,ymax:r.max};if(!n)return t._scaleRanges=i,!0;let a=n.xmin!==e.min||n.xmax!==e.max||n.ymin!==r.min||n.ymax!==r.max;return Object.assign(n,i),a}let tX=t=>0===t||1===t,tV=(t,e,r)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*ti/r)),tZ=(t,e,r)=>Math.pow(2,-10*t)*Math.sin((t-e)*ti/r)+1,tQ={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*ts)+1,easeOutSine:t=>Math.sin(t*ts),easeInOutSine:t=>-.5*(Math.cos(tn*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>tX(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>tX(t)?t:tV(t,.075,.3),easeOutElastic:t=>tX(t)?t:tZ(t,.075,.3),easeInOutElastic:t=>tX(t)?t:t<.5?.5*tV(2*t,.1125,.45):.5+.5*tZ(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-tQ.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*tQ.easeInBounce(2*t):.5*tQ.easeOutBounce(2*t-1)+.5};function tU(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function tK(t){return tU(t)?t:new j(t)}function tJ(t){return tU(t)?t:new j(t).saturate(.5).darken(.1).hexString()}let tG=["x","y","borderWidth","radius","tension"],t0=["color","borderColor","backgroundColor"],t1=new Map;function t2(t,e,r){return(function(t,e){let r=t+JSON.stringify(e=e||{}),n=t1.get(r);return n||(n=new Intl.NumberFormat(t,e),t1.set(r,n)),n})(e,r).format(t)}let t5={values:t=>W(t)?t:""+t,numeric(t,e,r){let n;if(0===t)return"0";let i=this.chart.options.locale,a=t;if(r.length>1){var o,l;let e,i=Math.max(Math.abs(r[0].value),Math.abs(r[r.length-1].value));(i<1e-4||i>1e15)&&(n="scientific"),o=t,Math.abs(e=(l=r).length>3?l[2].value-l[1].value:l[1].value-l[0].value)>=1&&o!==Math.floor(o)&&(e=o-Math.floor(o)),a=e}let s=tu(Math.abs(a)),c=isNaN(s)?1:Math.max(Math.min(-1*Math.floor(s),20),0),f={notation:n,minimumFractionDigits:c,maximumFractionDigits:c};return Object.assign(f,this.options.ticks.format),t2(t,i,f)},logarithmic(t,e,r){return 0===t?"0":[1,2,3,5,10,15].includes(r[e].significand||t/Math.pow(10,Math.floor(tu(t))))||e>.8*r.length?t5.numeric.call(this,t,e,r):""}};var t4={formatters:t5};let t8=Object.create(null),t6=Object.create(null);function t7(t,e){if(!e)return t;let r=e.split(".");for(let e=0,n=r.length;e<n;++e){let n=r[e];t=t[n]||(t[n]=Object.create(null))}return t}function t3(t,e,r){return"string"==typeof e?V(t7(t,e),r):V(t7(t,""),e)}class t9{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>tJ(e.backgroundColor),this.hoverBorderColor=(t,e)=>tJ(e.borderColor),this.hoverColor=(t,e)=>tJ(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return t3(this,t,e)}get(t){return t7(this,t)}describe(t,e){return t3(t6,t,e)}override(t,e){return t3(t8,t,e)}route(t,e,r,n){let i=t7(this,t),a=t7(this,r),o="_"+e;Object.defineProperties(i,{[o]:{value:i[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[o],e=a[n];return I(t)?Object.assign({},e,t):L(t,e)},set(t){this[o]=t}}})}apply(t){t.forEach(t=>t(this))}}var et=new t9({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:t0},numbers:{type:"number",properties:tG}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:t4.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function ee(t,e,r,n,i){let a=e[i];return a||(a=e[i]=t.measureText(i).width,r.push(i)),a>n&&(n=a),n}function er(t,e,r,n){let i,a,o,l,s,c=(n=n||{}).data=n.data||{},f=n.garbageCollect=n.garbageCollect||[];n.font!==e&&(c=n.data={},f=n.garbageCollect=[],n.font=e),t.save(),t.font=e;let u=0,h=r.length;for(i=0;i<h;i++)if(null==(l=r[i])||W(l)){if(W(l))for(a=0,o=l.length;a<o;a++)null==(s=l[a])||W(s)||(u=ee(t,c,f,u,s))}else u=ee(t,c,f,u,l);t.restore();let d=f.length/2;if(d>r.length){for(i=0;i<d;i++)delete c[f[i]];f.splice(0,d)}return u}function en(t,e,r){let n=t.currentDevicePixelRatio,i=0!==r?Math.max(r/2,.5):0;return Math.round((e-i)*n)/n+i}function ei(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function ea(t,e,r,n){eo(t,e,r,n,null)}function eo(t,e,r,n,i){let a,o,l,s,c,f,u,h,d=e.pointStyle,p=e.rotation,g=e.radius,b=(p||0)*tl;if(d&&"object"==typeof d&&("[object HTMLImageElement]"===(a=d.toString())||"[object HTMLCanvasElement]"===a)){t.save(),t.translate(r,n),t.rotate(b),t.drawImage(d,-d.width/2,-d.height/2,d.width,d.height),t.restore();return}if(!isNaN(g)&&!(g<=0)){switch(t.beginPath(),d){default:i?t.ellipse(r,n,i/2,g,0,0,ti):t.arc(r,n,g,0,ti),t.closePath();break;case"triangle":f=i?i/2:g,t.moveTo(r+Math.sin(b)*f,n-Math.cos(b)*g),b+=tf,t.lineTo(r+Math.sin(b)*f,n-Math.cos(b)*g),b+=tf,t.lineTo(r+Math.sin(b)*f,n-Math.cos(b)*g),t.closePath();break;case"rectRounded":c=.516*g,o=Math.cos(b+tc)*(s=g-c),u=Math.cos(b+tc)*(i?i/2-c:s),l=Math.sin(b+tc)*s,h=Math.sin(b+tc)*(i?i/2-c:s),t.arc(r-u,n-l,c,b-tn,b-ts),t.arc(r+h,n-o,c,b-ts,b),t.arc(r+u,n+l,c,b,b+ts),t.arc(r-h,n+o,c,b+ts,b+tn),t.closePath();break;case"rect":if(!p){s=Math.SQRT1_2*g,f=i?i/2:s,t.rect(r-f,n-s,2*f,2*s);break}b+=tc;case"rectRot":u=Math.cos(b)*(i?i/2:g),o=Math.cos(b)*g,l=Math.sin(b)*g,h=Math.sin(b)*(i?i/2:g),t.moveTo(r-u,n-l),t.lineTo(r+h,n-o),t.lineTo(r+u,n+l),t.lineTo(r-h,n+o),t.closePath();break;case"crossRot":b+=tc;case"cross":u=Math.cos(b)*(i?i/2:g),o=Math.cos(b)*g,l=Math.sin(b)*g,h=Math.sin(b)*(i?i/2:g),t.moveTo(r-u,n-l),t.lineTo(r+u,n+l),t.moveTo(r+h,n-o),t.lineTo(r-h,n+o);break;case"star":u=Math.cos(b)*(i?i/2:g),o=Math.cos(b)*g,l=Math.sin(b)*g,h=Math.sin(b)*(i?i/2:g),t.moveTo(r-u,n-l),t.lineTo(r+u,n+l),t.moveTo(r+h,n-o),t.lineTo(r-h,n+o),b+=tc,u=Math.cos(b)*(i?i/2:g),o=Math.cos(b)*g,l=Math.sin(b)*g,h=Math.sin(b)*(i?i/2:g),t.moveTo(r-u,n-l),t.lineTo(r+u,n+l),t.moveTo(r+h,n-o),t.lineTo(r-h,n+o);break;case"line":o=i?i/2:Math.cos(b)*g,l=Math.sin(b)*g,t.moveTo(r-o,n-l),t.lineTo(r+o,n+l);break;case"dash":t.moveTo(r,n),t.lineTo(r+Math.cos(b)*(i?i/2:g),n+Math.sin(b)*g);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function el(t,e,r){return r=r||.5,!e||t&&t.x>e.left-r&&t.x<e.right+r&&t.y>e.top-r&&t.y<e.bottom+r}function es(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function ec(t){t.restore()}function ef(t,e,r,n,i){if(!e)return t.lineTo(r.x,r.y);if("middle"===i){let n=(e.x+r.x)/2;t.lineTo(n,e.y),t.lineTo(n,r.y)}else"after"===i!=!!n?t.lineTo(e.x,r.y):t.lineTo(r.x,e.y);t.lineTo(r.x,r.y)}function eu(t,e,r,n){if(!e)return t.lineTo(r.x,r.y);t.bezierCurveTo(n?e.cp1x:e.cp2x,n?e.cp1y:e.cp2y,n?r.cp2x:r.cp1x,n?r.cp2y:r.cp1y,r.x,r.y)}function eh(t,e,r,n,i,a={}){let o,l,s=W(e)?e:[e],c=a.strokeWidth>0&&""!==a.strokeColor;for(t.save(),t.font=i.string,a.translation&&t.translate(a.translation[0],a.translation[1]),E(a.rotation)||t.rotate(a.rotation),a.color&&(t.fillStyle=a.color),a.textAlign&&(t.textAlign=a.textAlign),a.textBaseline&&(t.textBaseline=a.textBaseline),o=0;o<s.length;++o)l=s[o],a.backdrop&&function(t,e){let r=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=r}(t,a.backdrop),c&&(a.strokeColor&&(t.strokeStyle=a.strokeColor),E(a.strokeWidth)||(t.lineWidth=a.strokeWidth),t.strokeText(l,r,n,a.maxWidth)),t.fillText(l,r,n,a.maxWidth),function(t,e,r,n,i){if(i.strikethrough||i.underline){let a=t.measureText(n),o=e-a.actualBoundingBoxLeft,l=e+a.actualBoundingBoxRight,s=r-a.actualBoundingBoxAscent,c=r+a.actualBoundingBoxDescent,f=i.strikethrough?(s+c)/2:c;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=i.decorationWidth||2,t.moveTo(o,f),t.lineTo(l,f),t.stroke()}}(t,r,n,l,a),n+=Number(i.lineHeight);t.restore()}function ed(t,e){let{x:r,y:n,w:i,h:a,radius:o}=e;t.arc(r+o.topLeft,n+o.topLeft,o.topLeft,1.5*tn,tn,!0),t.lineTo(r,n+a-o.bottomLeft),t.arc(r+o.bottomLeft,n+a-o.bottomLeft,o.bottomLeft,tn,ts,!0),t.lineTo(r+i-o.bottomRight,n+a),t.arc(r+i-o.bottomRight,n+a-o.bottomRight,o.bottomRight,ts,0,!0),t.lineTo(r+i,n+o.topRight),t.arc(r+i-o.topRight,n+o.topRight,o.topRight,0,-ts,!0),t.lineTo(r+o.topLeft,n)}let ep=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,eg=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,eb=t=>+t||0;function ey(t,e){let r={},n=I(e),i=n?Object.keys(e):e,a=I(t)?n?r=>L(t[r],t[e[r]]):e=>t[e]:()=>t;for(let t of i)r[t]=eb(a(t));return r}function em(t){return ey(t,{top:"y",right:"x",bottom:"y",left:"x"})}function ex(t){return ey(t,["topLeft","topRight","bottomLeft","bottomRight"])}function ek(t){let e=em(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function ev(t,e){t=t||{},e=e||et.font;let r=L(t.size,e.size);"string"==typeof r&&(r=parseInt(r,10));let n=L(t.style,e.style);n&&!(""+n).match(eg)&&(console.warn('Invalid font style specified: "'+n+'"'),n=void 0);let i={family:L(t.family,e.family),lineHeight:function(t,e){let r=(""+t).match(ep);if(!r||"normal"===r[1])return 1.2*e;switch(t=+r[2],r[3]){case"px":return t;case"%":t/=100}return e*t}(L(t.lineHeight,e.lineHeight),r),size:r,style:n,weight:L(t.weight,e.weight),string:""};return i.string=!i||E(i.size)||E(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family,i}function eM(t,e,r,n){let i,a,o,l=!0;for(i=0,a=t.length;i<a;++i)if(void 0!==(o=t[i])&&(void 0!==e&&"function"==typeof o&&(o=o(e),l=!1),void 0!==r&&W(o)&&(o=o[r%o.length],l=!1),void 0!==o))return n&&!l&&(n.cacheable=!1),o}function ew(t,e,r){let{min:n,max:i}=t,a=B(e,(i-n)/2),o=(t,e)=>r&&0===t?0:t+e;return{min:o(n,-Math.abs(a)),max:o(i,a)}}function eO(t,e){return Object.assign(Object.create(t),e)}function e_(t,e=[""],r,n,i=()=>t[0]){let a=r||t;return void 0===n&&(n=eE("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:a,_fallback:n,_getTarget:i,override:r=>e_([r,...t],e,a,n)},{deleteProperty:(e,r)=>(delete e[r],delete e._keys,delete t[0][r],!0),get:(r,n)=>eS(r,n,()=>(function(t,e,r,n){let i;for(let a of e)if(void 0!==(i=eE(eT(a,t),r)))return eP(t,i)?eC(r,n,t,i):i})(n,e,t,r)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>eW(t).includes(e),ownKeys:t=>eW(t),set(t,e,r){let n=t._storage||(t._storage=i());return t[e]=n[e]=r,delete t._keys,!0}})}function eA(t,e={scriptable:!0,indexable:!0}){let{_scriptable:r=e.scriptable,_indexable:n=e.indexable,_allKeys:i=e.allKeys}=t;return{allKeys:i,scriptable:r,indexable:n,isScriptable:tt(r)?r:()=>r,isIndexable:tt(n)?n:()=>n}}let eT=(t,e)=>t?t+J(e):e,eP=(t,e)=>I(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function eS(t,e,r){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let n=r();return t[e]=n,n}let ej=(t,e)=>!0===t?e:"string"==typeof t?K(e,t):void 0;function eC(t,e,r,n){var i;let a=e._rootScopes,o=(i=e._fallback,tt(i)?i(r,n):i),l=[...t,...a],s=new Set;s.add(n);let c=eR(s,l,r,o||r,n);return null!==c&&(void 0===o||o===r||null!==(c=eR(s,l,o,c,n)))&&e_(Array.from(s),[""],a,o,()=>(function(t,e,r){let n=t._getTarget();e in n||(n[e]={});let i=n[e];return W(i)&&I(r)?r:i||{}})(e,r,n))}function eR(t,e,r,n,i){for(;r;)r=function(t,e,r,n,i){for(let o of e){let e=ej(r,o);if(e){var a;t.add(e);let o=(a=e._fallback,tt(a)?a(r,i):a);if(void 0!==o&&o!==r&&o!==n)return o}else if(!1===e&&void 0!==n&&r!==n)return null}return!1}(t,e,r,n,i);return r}function eE(t,e){for(let r of e){if(!r)continue;let e=r[t];if(void 0!==e)return e}}function eW(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let r of t)for(let t of Object.keys(r).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function eI(t,e,r,n){let i,a,o,{iScale:l}=t,{key:s="r"}=this._parsing,c=Array(n);for(i=0;i<n;++i)o=e[a=i+r],c[i]={r:l.parse(K(o,s),a)};return c}let eF=Number.EPSILON||1e-14,eN=(t,e)=>e<t.length&&!t[e].skip&&t[e],eL=t=>"x"===t?"y":"x";function eY(t,e,r){return Math.max(Math.min(t,r),e)}function eB(t,e,r,n,i){let a,o,l,s;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let r,n,i,a=eL(e),o=t.length,l=Array(o).fill(0),s=Array(o),c=eN(t,0);for(r=0;r<o;++r)if(n=i,i=c,c=eN(t,r+1),i){if(c){let t=c[e]-i[e];l[r]=0!==t?(c[a]-i[a])/t:0}s[r]=n?c?th(l[r-1])!==th(l[r])?0:(l[r-1]+l[r])/2:l[r-1]:l[r]}!function(t,e,r){let n,i,a,o,l,s=t.length,c=eN(t,0);for(let f=0;f<s-1;++f)if(l=c,c=eN(t,f+1),l&&c){if(td(e[f],0,eF)){r[f]=r[f+1]=0;continue}(o=Math.pow(n=r[f]/e[f],2)+Math.pow(i=r[f+1]/e[f],2))<=9||(a=3/Math.sqrt(o),r[f]=n*a*e[f],r[f+1]=i*a*e[f])}}(t,l,s),function(t,e,r="x"){let n,i,a,o=eL(r),l=t.length,s=eN(t,0);for(let c=0;c<l;++c){if(i=a,a=s,s=eN(t,c+1),!a)continue;let l=a[r],f=a[o];i&&(n=(l-i[r])/3,a[`cp1${r}`]=l-n,a[`cp1${o}`]=f-n*e[c]),s&&(n=(s[r]-l)/3,a[`cp2${r}`]=l+n,a[`cp2${o}`]=f+n*e[c])}}(t,s,e)}(t,i);else{let r=n?t[t.length-1]:t[0];for(a=0,o=t.length;a<o;++a)s=function(t,e,r,n){let i=t.skip?e:t,a=r.skip?e:r,o=tw(e,i),l=tw(a,e),s=o/(o+l),c=l/(o+l);s=isNaN(s)?0:s,c=isNaN(c)?0:c;let f=n*s,u=n*c;return{previous:{x:e.x-f*(a.x-i.x),y:e.y-f*(a.y-i.y)},next:{x:e.x+u*(a.x-i.x),y:e.y+u*(a.y-i.y)}}}(r,l=t[a],t[Math.min(a+1,o-!n)%o],e.tension),l.cp1x=s.previous.x,l.cp1y=s.previous.y,l.cp2x=s.next.x,l.cp2y=s.next.y,r=l}e.capBezierPoints&&function(t,e){let r,n,i,a,o,l=el(t[0],e);for(r=0,n=t.length;r<n;++r)o=a,a=l,l=r<n-1&&el(t[r+1],e),a&&(i=t[r],o&&(i.cp1x=eY(i.cp1x,e.left,e.right),i.cp1y=eY(i.cp1y,e.top,e.bottom)),l&&(i.cp2x=eY(i.cp2x,e.left,e.right),i.cp2y=eY(i.cp2y,e.top,e.bottom)))}(t,r)}function eH(){return"undefined"!=typeof window&&"undefined"!=typeof document}function e$(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function eD(t,e,r){let n;return"string"==typeof t?(n=parseInt(t,10),-1!==t.indexOf("%")&&(n=n/100*e.parentNode[r])):n=t,n}let ez=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),eq=["top","right","bottom","left"];function eX(t,e,r){let n={};r=r?"-"+r:"";for(let i=0;i<4;i++){let a=eq[i];n[a]=parseFloat(t[e+"-"+a+r])||0}return n.width=n.left+n.right,n.height=n.top+n.bottom,n}function eV(t,e){if("native"in t)return t;let{canvas:r,currentDevicePixelRatio:n}=e,i=ez(r),a="border-box"===i.boxSizing,o=eX(i,"padding"),l=eX(i,"border","width"),{x:s,y:c,box:f}=function(t,e){let r,n,i,a=t.touches,o=a&&a.length?a[0]:t,{offsetX:l,offsetY:s}=o,c=!1;if(i=t.target,(l>0||s>0)&&(!i||!i.shadowRoot))r=l,n=s;else{let t=e.getBoundingClientRect();r=o.clientX-t.left,n=o.clientY-t.top,c=!0}return{x:r,y:n,box:c}}(t,r),u=o.left+(f&&l.left),h=o.top+(f&&l.top),{width:d,height:p}=e;return a&&(d-=o.width+l.width,p-=o.height+l.height),{x:Math.round((s-u)/d*r.width/n),y:Math.round((c-h)/p*r.height/n)}}let eZ=t=>Math.round(10*t)/10;function eQ(t,e,r,n){let i=ez(t),a=eX(i,"margin"),o=eD(i.maxWidth,t,"clientWidth")||to,l=eD(i.maxHeight,t,"clientHeight")||to,s=function(t,e,r){let n,i;if(void 0===e||void 0===r){let a=t&&e$(t);if(a){let t=a.getBoundingClientRect(),o=ez(a),l=eX(o,"border","width"),s=eX(o,"padding");e=t.width-s.width-l.width,r=t.height-s.height-l.height,n=eD(o.maxWidth,a,"clientWidth"),i=eD(o.maxHeight,a,"clientHeight")}else e=t.clientWidth,r=t.clientHeight}return{width:e,height:r,maxWidth:n||to,maxHeight:i||to}}(t,e,r),{width:c,height:f}=s;if("content-box"===i.boxSizing){let t=eX(i,"border","width"),e=eX(i,"padding");c-=e.width+t.width,f-=e.height+t.height}return c=Math.max(0,c-a.width),f=Math.max(0,n?c/n:f-a.height),c=eZ(Math.min(c,o,s.maxWidth)),f=eZ(Math.min(f,l,s.maxHeight)),c&&!f&&(f=eZ(c/2)),(void 0!==e||void 0!==r)&&n&&s.height&&f>s.height&&(c=eZ(Math.floor((f=s.height)*n))),{width:c,height:f}}function eU(t,e,r){let n=e||1,i=Math.floor(t.height*n),a=Math.floor(t.width*n);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let o=t.canvas;return o.style&&(r||!o.style.height&&!o.style.width)&&(o.style.height=`${t.height}px`,o.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==n||o.height!==i||o.width!==a)&&(t.currentDevicePixelRatio=n,o.height=i,o.width=a,t.ctx.setTransform(n,0,0,n,0,0),!0)}let eK=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};eH()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function eJ(t,e){let r=ez(t).getPropertyValue(e),n=r&&r.match(/^(\d+)(\.\d+)?px$/);return n?+n[1]:void 0}function eG(t,e,r,n){return{x:t.x+r*(e.x-t.x),y:t.y+r*(e.y-t.y)}}function e0(t,e,r,n){return{x:t.x+r*(e.x-t.x),y:"middle"===n?r<.5?t.y:e.y:"after"===n?r<1?t.y:e.y:r>0?e.y:t.y}}function e1(t,e,r,n){let i={x:t.cp2x,y:t.cp2y},a={x:e.cp1x,y:e.cp1y},o=eG(t,i,r),l=eG(i,a,r),s=eG(a,e,r),c=eG(o,l,r),f=eG(l,s,r);return eG(c,f,r)}function e2(t,e,r){var n;return t?(n=r,{x:t=>e+e+n-t,setWidth(t){n=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function e5(t,e){let r,n;("ltr"===e||"rtl"===e)&&(n=[(r=t.canvas.style).getPropertyValue("direction"),r.getPropertyPriority("direction")],r.setProperty("direction",e,"important"),t.prevTextDirection=n)}function e4(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function e8(t){return"angle"===t?{between:tA,compare:tO,normalize:t_}:{between:tS,compare:(t,e)=>t-e,normalize:t=>t}}function e6({start:t,end:e,count:r,loop:n,style:i}){return{start:t%r,end:e%r,loop:n&&(e-t+1)%r==0,style:i}}function e7(t,e,r){let n,i,a;if(!r)return[t];let{property:o,start:l,end:s}=r,c=e.length,{compare:f,between:u,normalize:h}=e8(o),{start:d,end:p,loop:g,style:b}=function(t,e,r){let n,{property:i,start:a,end:o}=r,{between:l,normalize:s}=e8(i),c=e.length,{start:f,end:u,loop:h}=t;if(h){for(f+=c,u+=c,n=0;n<c&&l(s(e[f%c][i]),a,o);++n)f--,u--;f%=c,u%=c}return u<f&&(u+=c),{start:f,end:u,loop:h,style:t.style}}(t,e,r),y=[],m=!1,x=null,k=()=>m||u(l,a,n)&&0!==f(l,a),v=()=>!m||0===f(s,n)||u(s,a,n);for(let t=d,r=d;t<=p;++t)(i=e[t%c]).skip||(n=h(i[o]))!==a&&(m=u(n,l,s),null===x&&k()&&(x=0===f(n,l)?t:r),null!==x&&v()&&(y.push(e6({start:x,end:t,loop:g,count:c,style:b})),x=null),r=t,a=n);return null!==x&&y.push(e6({start:x,end:p,loop:g,count:c,style:b})),y}function e3(t,e){let r=[],n=t.segments;for(let i=0;i<n.length;i++){let a=e7(n[i],t.points,e);a.length&&r.push(...a)}return r}function e9(t,e){let r=t.points,n=t.options.spanGaps,i=r.length;if(!i)return[];let a=!!t._loop,{start:o,end:l}=function(t,e,r,n){let i=0,a=e-1;if(r&&!n)for(;i<e&&!t[i].skip;)i++;for(;i<e&&t[i].skip;)i++;for(i%=e,r&&(a+=i);a>i&&t[a%e].skip;)a--;return{start:i,end:a%=e}}(r,i,a,n);if(!0===n)return rt(t,[{start:o,end:l,loop:a}],r,e);let s=l<o?l+i:l,c=!!t._fullLoop&&0===o&&l===i-1;return rt(t,function(t,e,r,n){let i,a=t.length,o=[],l=e,s=t[e];for(i=e+1;i<=r;++i){let r=t[i%a];r.skip||r.stop?s.skip||(n=!1,o.push({start:e%a,end:(i-1)%a,loop:n}),e=l=r.stop?i:null):(l=i,s.skip&&(e=i)),s=r}return null!==l&&o.push({start:e%a,end:l%a,loop:n}),o}(r,o,s,c),r,e)}function rt(t,e,r,n){return n&&n.setContext&&r?function(t,e,r,n){let i=t._chart.getContext(),a=re(t.options),{_datasetIndex:o,options:{spanGaps:l}}=t,s=r.length,c=[],f=a,u=e[0].start,h=u;function d(t,e,n,i){let a=l?-1:1;if(t!==e){for(t+=s;r[t%s].skip;)t-=a;for(;r[e%s].skip;)e+=a;t%s!=e%s&&(c.push({start:t%s,end:e%s,loop:n,style:i}),f=i,u=e%s)}}for(let t of e){let e,a=r[(u=l?u:t.start)%s];for(h=u+1;h<=t.end;h++){let l=r[h%s];(function(t,e){if(!e)return!1;let r=[],n=function(t,e){return tU(e)?(r.includes(e)||r.push(e),r.indexOf(e)):e};return JSON.stringify(t,n)!==JSON.stringify(e,n)})(e=re(n.setContext(eO(i,{type:"segment",p0:a,p1:l,p0DataIndex:(h-1)%s,p1DataIndex:h%s,datasetIndex:o}))),f)&&d(u,h-1,t.loop,f),a=l,f=e}u<h-1&&d(u,h-1,t.loop,f)}return c}(t,e,r,n):e}function re(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function rr(t,e,r){return t.options.clip?t[r]:e[r]}function rn(t,e){let r=e._clip;if(r.disabled)return!1;let n=function(t,e){let{xScale:r,yScale:n}=t;return r&&n?{left:rr(r,e,"left"),right:rr(r,e,"right"),top:rr(n,e,"top"),bottom:rr(n,e,"bottom")}:e}(e,t.chartArea);return{left:!1===r.left?0:n.left-(!0===r.left?0:r.left),right:!1===r.right?t.width:n.right+(!0===r.right?0:r.right),top:!1===r.top?0:n.top-(!0===r.top?0:r.top),bottom:!1===r.bottom?t.height:n.bottom+(!0===r.bottom?0:r.bottom)}}},4033:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},5229:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5710:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},6132:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6191:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},6485:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},6983:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},7726:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},7828:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},7937:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},9476:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},9540:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},9867:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(1847).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])}}]);